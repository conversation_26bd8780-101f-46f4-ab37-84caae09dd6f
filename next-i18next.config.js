/** @type {import('next-i18next').UserConfig} */
module.exports = {
  // Note: i18n routing is not supported with static export
  // We'll handle locale switching client-side
  defaultLocale: 'ru',
  locales: ['en', 'ru'],
  localePath: typeof window === 'undefined' ? require('path').resolve('./public/locales') : '/locales',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  debug: process.env.NODE_ENV === 'development',
  interpolation: {
    escapeValue: false, // React already does escaping
  },
  react: {
    useSuspense: false, // Important for Next.js App Router compatibility
  },
}

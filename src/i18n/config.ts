'use client';

import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import enTranslation from './en/translation.json';
import ruTranslation from './ru/translation.json';

i18next.use(initReactI18next).init({
  lng: 'ru', // if you're using a language detector, do not define the lng option
  fallbackLng: 'en',
  debug: false,
  resources: {
    en: { translation: enTranslation, },
    ru: { translation: ruTranslation, },
  },
  interpolation: {
    escapeValue: false // React already safes from XSS
  }
  // if you see an error like: "Argument of type 'DefaultTFuncReturn' is not assignable to parameter of type xyz"
  // set returnNull to false (and also in the i18next.d.ts options)
  // returnNull: false,
});

export default i18next;


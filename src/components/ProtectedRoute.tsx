import React, {useEffect} from 'react';
import {Navigate, Outlet} from 'react-router-dom';
import {usePBContext} from "@/context/PocketbaseContext";
import {useReferral} from "@/hooks/use-referral";

interface ProtectedRouteProps {
    children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
    const { user } = usePBContext();

    // Hook must be called before any conditional returns
    const _ = useReferral();

    const isAuthenticated = user != null;

    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;

import {Zap, Star, Coins, Crown, Image, Video} from 'lucide-react';
import {useTranslation} from 'next-i18next';
import React from "react";

const Pricing = () => {
    const {t} = useTranslation();

    // Token pricing tiers
    const tokenPackages = [
        {
            tagline: 'Для начинающих',
            taglineBg: 'bg-emerald-500',
            taglineColor: 'text-white',
            name: t('pricing.tokenPackages.packages.small.name'),
            tokens: 100,
            price: t('pricing.tokenPackages.packages.small.price'),
            pricePerToken: t('pricing.tokenPackages.packages.small.pricePerToken'),
            description: t('pricing.tokenPackages.packages.small.description'),
            icon: Coins,
            popular: false,
            savings: null,
            buttonText: t('pricing.tokenPackages.packages.small.buttonText')
        },
        {
            tagline: 'Самый популярный',
            taglineBg: 'bg-blue-600',
            taglineColor: 'text-white',
            name: t('pricing.tokenPackages.packages.medium.name'),
            tokens: 600,
            price: '1300₽',
            originalPrice: '1800₽',
            pricePerToken: null,
            description: t('pricing.tokenPackages.packages.medium.description'),
            icon: Zap,
            popular: true,
            savings: t('pricing.tokenPackages.packages.medium.savings'),
            buttonText: t('pricing.tokenPackages.packages.medium.buttonText')
        },
        {
            tagline: 'Самый выгодный',
            taglineBg: 'bg-amber-500',
            taglineColor: 'text-white',
            name: t('pricing.tokenPackages.packages.large.name'),
            tokens: 1000,
            price: '2000₽',
            originalPrice: '3000₽',
            pricePerToken: null,
            description: t('pricing.tokenPackages.packages.large.description'),
            icon: Crown,
            popular: false,
            savings: t('pricing.tokenPackages.packages.large.savings'),
            buttonText: t('pricing.tokenPackages.packages.large.buttonText')
        }
    ];

  // Per-model token costs based on actual database prices
  const modelCosts = {
    image: [
      {
        name: t('pricing.modelCosts.imageModels.models.seedream3.name'),
        id: 'seedream-3',
        cost: t('pricing.modelCosts.imageModels.models.seedream3.cost'),
        description: t('pricing.modelCosts.imageModels.models.seedream3.description'),
        pricing: t('pricing.modelCosts.imageModels.models.seedream3.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.imagen4Ultra.name'),
        id: 'imagen-4-ultra',
        cost: t('pricing.modelCosts.imageModels.models.imagen4Ultra.cost'),
        description: t('pricing.modelCosts.imageModels.models.imagen4Ultra.description'),
        pricing: t('pricing.modelCosts.imageModels.models.imagen4Ultra.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.fluxKontextPro.name'),
        id: 'flux-kontext-pro',
        cost: t('pricing.modelCosts.imageModels.models.fluxKontextPro.cost'),
        description: t('pricing.modelCosts.imageModels.models.fluxKontextPro.description'),
        pricing: t('pricing.modelCosts.imageModels.models.fluxKontextPro.pricing')
      }
    ],
    video: [
      {
        name: t('pricing.modelCosts.videoModels.models.seedanceLite.name'),
        id: 'seedance-lite',
        variants: [
          { resolution: '480p', cost: 2, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.480p') },
          { resolution: '1080p', cost: 4, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedanceLite.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.seedancePro.name'),
        id: 'seedance-pro',
        variants: [
          { resolution: '480p', cost: 3, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.480p') },
          { resolution: '1080p', cost: 15, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedancePro.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.hunyuan.name'),
        id: 'hunyuan',
        cost: t('pricing.modelCosts.videoModels.models.hunyuan.cost'),
        description: t('pricing.modelCosts.videoModels.models.hunyuan.description'),
        pricing: t('pricing.modelCosts.videoModels.models.hunyuan.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo2.name'),
        id: 'veo2',
        cost: t('pricing.modelCosts.videoModels.models.veo2.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo2.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo2.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo3.name'),
        id: 'veo3',
        cost: t('pricing.modelCosts.videoModels.models.veo3.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo3.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo3.pricing')
      }
    ]
  };

    return (
        <section id="pricing" className="pt-20 bg-background-primary">
            <div className="container mx-auto px-4" >
                <div className="text-center mb-16">
                    <h1 className="text-4xl md:text-6xl font-bold mb-6 text-text-dark">
                        {t('pricing.title')}{' '}
                        <span className="text-gradient">{t('pricing.titleHighlight')}</span>
                    </h1>
                    <p className="text-xl text-text-light max-w-3xl mx-auto mb-8">
                        {t('pricing.subtitle')}
                    </p>
                </div>

                {/* Token Packages */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-20">
                    {tokenPackages.map((pkg, index) => (
                        <div
                            key={index}
                            className={`card relative rounded-2xl pt-6 p-8 transition-all duration-300 animate-fade-in-up border bg-background-secondary ${pkg.popular ? 'border-primary scale-105' : 'border-border-light'}`}
                            style={{animationDelay: `${index * 0.1}s`}}
                        >
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-full px-4">
                                <div
                                    className={`text-center text-base font-semibold py-2 rounded-full ${pkg.taglineBg} ${pkg.taglineColor}`}>
                                    {pkg.tagline}
                                </div>
                            </div>
                            <div className="text-center mb-8">
                                <div className="flex justify-center mb-4">
                                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                                        pkg.popular
                                            ? 'bg-primary'
                                            : 'bg-background-secondary'
                                    }`}>
                                        <pkg.icon className="w-8 h-8 text-white"/>
                                    </div>
                                </div>
                                <h3 className="text-2xl font-bold text-text-dark mb-2">{pkg.name}</h3>
                                <p className="text-text-light mb-4">{pkg.description}</p>
                                <div className="flex items-baseline justify-center gap-2 mb-2">
                                    {pkg.originalPrice && (
                                        <span className="text-2xl font-medium text-text-light line-through">{pkg.originalPrice}</span>
                                    )}
                                    <span className="text-4xl font-bold text-text-dark">{pkg.price}</span>
                                </div>
                                <div className="text-sm text-text-light">
                                    {pkg.pricePerToken && <span>{pkg.pricePerToken}</span>}
                                    {pkg.savings && (
                                        <span className="block text-primary font-bold text-lg mt-1">
                        {pkg.savings}
                      </span>
                                    )}
                                </div>
                            </div>

                            {/*<Button*/}
                            {/*    className={`w-full py-3 rounded-full font-semibold transition-all duration-300 ${*/}
                            {/*        pkg.popular*/}
                            {/*            ? 'bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white hover:shadow-lg hover:shadow-blue-500/25'*/}
                            {/*            : 'bg-transparent border-2 border-[var(--primary-blue)] text-[var(--primary-blue)] hover:bg-[var(--primary-blue)] hover:text-white'*/}
                            {/*    }`}*/}
                            {/*>*/}
                            {/*    {pkg.buttonText}*/}
                            {/*</Button>*/}
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default Pricing;

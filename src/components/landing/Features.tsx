import { useTranslation } from 'react-i18next';
import { FeatureShowcase } from './FeatureShowcase';
import { Sparkles, Shield, Cpu } from 'lucide-react';
import { useRouter } from 'next/navigation';
interface Feature {
  title: string,
  description: string,
  image: string,
  reverse: boolean,
}

interface FeatureProps {
  features?: Feature[]
}

const defaultFeatures = [
  {
    title: 'Создавайте контент для SMM',
    description: 'Искусственно созданное изображение мгновенно ускорит процесс вашего проекта. У вас спешка с подготовкой большой презентации и нужна уникальная картинка? Просто введите запрос в генератор изображений на основе ИИ — и идеальное изображение будет готово моментально.',
    image: 'influencer_sample.jpg',
    reverse: false
  },
  {
    title: 'Создавайте обложки для маркетплейсов',
    description: 'Представьте, что ваш товар на витрине сияет и притягивает взгляды. Опишите его — и искусственный интеллект создаст яркое изображение, готовое для маркетплейса. Ваш личный дизайнер, работающий по щелчку.',
    image: 'marketplace_sample.png',
    reverse: true
  },
  {
    title: 'Находите вдохновение',
    description: 'Представьте, что идеи сами находят вас. Опишите своё настроение — и искусственный интеллект подберёт образы, цвета и формы, чтобы разбудить креатив. Ваш личный проводник в мире вдохновения, всегда на расстоянии одного клика.',
    image: 'inspiration_sample.jpg',
    reverse: false
  },
  {
    title: 'Оживляйте изображения',
    description: 'Представьте, что ваше изображение начинает двигаться и дышать. Опишите идею — и искусственный интеллект превратит её в анимацию, которая цепляет взгляд. Ваш личный режиссёр, оживляющий картинки по вдохновению.',
    image: 'man_speaking.gif',
    reverse: true
  },
  {
    title: 'Создавайте AI блогеров',
    description: 'Представьте, что вы придумываете идеального инфлуэнсера: внешность, стиль, харизма. Опишите его — и искусственный интеллект создаст цифровую личность, готовую сиять в Instagram или OnlyFans. Ваш собственный виртуальный герой, рождённый для лайков и внимания.',
    image: 'instagram_sample.jpg',
    reverse: false
  }
]

const Features = ({ features }: FeatureProps) => {
  const { t } = useTranslation();
  const router = useRouter();

  const featuresList = features || defaultFeatures;

  return (
    <section id="features" className="bg-white w-full lg:pb-20 pb-20">
      <div className="w-full">
        <div className="container mx-auto px-6 lg:px-8 py-20 lg:py-20">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-text-dark">
              {t('features.secondTitle')}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Sparkles className="w-8 h-8" />,
                title: t('features.items.aiPowered.title'),
                description: t('features.items.aiPowered.description')
              },
              {
                icon: <Shield className="w-8 h-8" />,
                title: t('features.items.commercialLicense.title'),
                description: t('features.items.commercialLicense.description')
              },
              {
                icon: <Cpu className="w-8 h-8" />,
                title: t('features.items.advancedModels.title'),
                description: t('features.items.advancedModels.description')
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="card items-center flex flex-col text-center animate-fade-in-up p-8 rounded-2xl shadow-2xl border border-gray-200 bg-background-secondary hover:bg-background-tertiary transition-colors duration-300"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="gradient-primary w-16 h-16 rounded-xl flex items-center justify-center mb-6 text-white">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-4 text-text-dark">{feature.title}</h3>
                <p className="text-text-light leading-relaxed">{feature.description}</p>
              </div>
            )
            )}
          </div>
          {/* CTA Button */}
          <div className="mb-8 w-full text-center pt-8">
            <button
              onClick={() => router.push('/studio')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              {t('hero.startCreating')}
            </button>
          </div>
        </div>
      </div>

      <div className="w-full">
        <div className="container mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-text-dark">
              {t('features.title')}{' '}
              <span className="bg-gradient-to-r from-blue-800 to-blue-400 bg-clip-text text-transparent">{t('features.titleHighlight')}</span>
            </h2>
            <p className="text-xl text-text-light max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </div>
        </div>
      </div>

      {featuresList.map((feature) => {
        return <div className="w-full" key={feature.title}>
          <div className="max-w-6xl mx-auto px-6 lg:px-8">
            <FeatureShowcase
              title={feature.title}
              description={feature.description}
              image={{
                type: 'composite-showcase',
                elements: [
                  {
                    type: 'generated-image',
                    subject: feature.image,
                    position: 'main'
                  }
                ]
              }}
              reverse={feature.reverse}
              className={feature.reverse ? "bottom-showcase" : ""}
            />
          </div>
        </div>
      })}
    </section>
  );
};

export default Features;

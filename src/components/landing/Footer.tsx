'use client';

import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const Footer = () => {
  const { t } = useTranslation();
  const router = useRouter();

  const scrollToTop = (e: React.MouseEvent, to: string) => {
    e.preventDefault();
    window.scrollTo(0, 0);
    router.push(to);
  };

  const legalLinks = [
    { name: t('footer.sections.legal.privacyPolicy'), href: '/privacy-policy', isRoute: true },
    { name: t('footer.sections.legal.termsOfService'), href: '/terms-of-use', isRoute: true },
  ];

  const pageLinks = [
    { name: t('landings.smm.pageName'), href: '/smm', isRoute: true },
    { name: t('landings.marketplace.pageName'), href: '/marketplace', isRoute: true },
    { name: t('landings.inspiration.pageName'), href: '/inspiration', isRoute: true },
    { name: t('landings.livingPhoto.pageName'), href: '/living-photo', isRoute: true },
    { name: t('landings.influencer.pageName'), href: '/influencer', isRoute: true },
  ];

  return (
    <footer className="bg-background-secondary">
      <div className="container mx-auto px-4 pb-8">
        {/* Bottom Bar */}
        <div className=" mt-12 pt-8">
          <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:max-w-[200px]">
              <p className="text-text-light text-sm">
                {t('footer.copyright')}
              </p>
            </div>

            {/* Page Links - Center */}
            <div className="flex flex-col items-center gap-2 mb-4 md:mb-0">
              {pageLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  onClick={(e) => scrollToTop(e, link.href)}
                  className="text-text-light hover:text-primary transition-colors duration-200 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>

            {/* Legal Links - Right */}
            <div className="flex flex-col items-end gap-2">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  onClick={(e) => scrollToTop(e, link.href)}
                  className="text-text-light hover:text-primary transition-colors duration-200 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

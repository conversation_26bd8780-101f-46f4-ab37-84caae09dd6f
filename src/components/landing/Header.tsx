'use client';

import { useState } from 'react';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';

interface NavItem {
  name: string;
  href: string;
  isRoute?: boolean;
}

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const { t } = useTranslation();

  const navItems: NavItem[] = [
    { name: t('header.navigation.features'), href: '#features' },
    // { name: t('header.navigation.gallery'), href: '#gallery' },
    // { name: t('header.navigation.pricing'), href: '#pricing' },
    { name: t('header.navigation.faq'), href: '#faq' },
  ];

  const handleNavClick = (href: string, isRoute?: boolean) => {
    if (isRoute) {
      // Navigate to route
      router.push(href);
    } else {
      // Scroll to section
      const targetId = href.replace('#', '');
      const targetSection = document.getElementById(targetId);
      if (targetSection) {
        targetSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
    // Close mobile menu if open
    setIsMenuOpen(false);
  };

  const handleGetStartedClick = () => {
    router.push('/studio');
  };

  return (
    <header className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-[10px] border-b border-border-light">
      <div className="px-6 lg:px-20 w-full">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2 cursor-pointer" onClick={()=>{
            router.push('/');
          }}>
            <img
              src="/ForDarkBG1.png"
              alt="Logo"
              className="w-8 h-8 object-contain"
            />
            <span className="text-xl font-bold text-text-dark">{t('header.brand')}</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <button
                key={item.name}
                onClick={() => handleNavClick(item.href, item.isRoute)}
                className="text-text-light hover:text-text-dark font-bold transition-colors duration-200"
              >
                {item.name}
              </button>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <button
              onClick={handleGetStartedClick}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              {t('header.getStarted')}
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 text-text-light hover:text-text-dark"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`md:hidden overflow-hidden transition-all duration-100 ease-out ${
            isMenuOpen
              ? 'max-h-96 opacity-100 border-t border-border-light'
              : 'max-h-0 opacity-0'
          }`}
        >
          <div className={`py-4 transform transition-transform duration-100 ease-out ${
            isMenuOpen ? 'translate-y-0' : '-translate-y-2'
          }`}>
            <nav className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => handleNavClick(item.href, item.isRoute)}
                  className="text-text-light hover:text-text-dark text-left"
                >
                  {item.name}
                </button>
              ))}
              <button
                onClick={handleGetStartedClick}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold w-full mt-4"
              >
                {t('header.getStarted')}
              </button>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;

import { Check } from 'lucide-react';
import { useTranslation } from 'next-i18next';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

interface HeroProps {
  title?: string;
  titleHighlight?: string;
  subtitle?: string;
  serviceImage?: string;
  secondaryImage?: string;
  topSecondaryImage?: string;
  bottomSecondaryImage?: string;
}

const Hero = ({ title, titleHighlight, subtitle, serviceImage, secondaryImage, topSecondaryImage, bottomSecondaryImage }: HeroProps) => {
  const router = useRouter();
  const { t } = useTranslation();

  const heroTitle = title || t('hero.title');
  const heroTitleHighlight = titleHighlight || t('hero.titleHighlight');
  const heroSubtitle = subtitle || t('hero.subtitle');
  const heroServiceImage = serviceImage || '/heroImages/landingHero1.png';
  const heroSecondaryServiceImage = secondaryImage || '/heroImages/landingHero2.png';
  const heroTopSecondaryImage = topSecondaryImage || '/heroImages/landingHero3.png';
  const heroBottomSecondaryImage = bottomSecondaryImage || '/heroImages/landingHero4.png';

  const handleStartCreatingClick = () => {
    router.push('/studio');
  };

  return (
      <section className="bg-white h-screen flex items-center overflow-hidden">
        <div className="w-full">
          <div className="grid lg:grid-cols-12 items-center max-w-none h-full">
            {/* Left Column */}
            <div className="lg:col-span-5 px-6 lg:pl-20 py-12">
              <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                {heroTitle}
                <br />
                <span className="bg-gradient-to-r from-blue-800 to-blue-400 bg-clip-text text-transparent">
                {heroTitleHighlight}
              </span>
              </h1>
              <p className="text-lg lg:text-xl text-gray-600 mb-8 leading-relaxed max-w-lg">
                {heroSubtitle}
              </p>
              <div className="mb-8">
                <button
                    onClick={handleStartCreatingClick}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  {t('hero.startCreating')}
                </button>
              </div>
              <div className="flex flex-col sm:flex-row gap-6 text-gray-700">
                {['hero.noCardRequired', 'hero.noVPNRequired', 'hero.noSubscription'].map((key) => (
                    <div key={key} className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-blue-600" />
                      </div>
                      <span className="font-medium">{t(key)}</span>
                    </div>
                ))}
              </div>
            </div>

            {/* Right Column */}
            <div className="lg:col-span-7 relative hidden lg:block h-full">
              <div className="relative h-full flex items-center justify-center px-8 py-12">
                {/* Main Image Container */}
                <div className="relative w-full max-w-2x">
                  {/* Main Image */}
                  <div className="rounded-2xl shadow-2xl border border-gray-200 overflow-hidden aspect-auto">
                    <img
                        src={heroServiceImage}
                        alt="Service screenshot"
                        className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Bottom-left image */}
                  <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
                      className="absolute -bottom-6 -left-6 z-10"
                  >
                    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-72">
                      <img src={heroSecondaryServiceImage} alt="Service screenshot" className="w-full h-full object-cover" />
                    </div>
                  </motion.div>

                  {/* Top-center image */}
                  <motion.div
                      initial={{ opacity: 0, y: -30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
                      className="absolute -top-6 left-1/4 transform -translate-x-1/2 z-10"
                  >
                    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-52">
                      <img src={heroTopSecondaryImage} alt="Service screenshot" className="w-full h-full object-cover" />
                    </div>
                  </motion.div>

                  {/* Bottom-right image */}
                  <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                      className="absolute -bottom-8 -right-8 z-10"
                  >
                    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden w-72">
                      <img src={heroBottomSecondaryImage} alt="Service screenshot" className="w-full h-full object-cover" />
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
  );
};

export default Hero;

import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from "react-i18next";

const Index = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-background-primary">
      <Helmet>
        <title>{t("seo.main.title")}</title>
        <meta name="description" content={t("seo.main.description")} />
        <meta name="keywords" content={t("seo.main.keywords")} />
        <meta property="og:title" content={t("seo.main.title")} />
        <meta property="og:description" content={t("seo.main.description")} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={t("seo.main.title")} />
        <meta name="twitter:description" content={t("seo.main.description")} />
      </Helmet>
      <Header/>
      <Hero/>
      <Features/>
      <FAQ/>
      <Footer/>
    </div>
  );
};

export default Index;

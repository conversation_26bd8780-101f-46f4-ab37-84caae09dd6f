import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import { useTranslation } from "next-i18next";
import { Helmet } from 'react-helmet-async';

const features = [
    {
        title: 'Анимация объектов на фото',
        description: 'ИИ превращает статичные элементы в двигающиеся — например, колышущиеся волосы, листья на дереве или мерцающий свет.',
        image: 'livingPhoto/1.webp',
        reverse: false
    },
    {
        title: 'Создание движущейся атмосферы',
        description: 'Добавление эффекта дождя, снега, тумана или света, чтобы фото выглядело живым и атмосферным.',
        image: 'livingPhoto/2.webp',
        reverse: true
    },
    {
        title: 'Оживление людей и персонажей',
        description: 'ИИ умеет анимировать лица, создавая лёгкие мимические движения, улыбки или взгляд в камеру, не теряя естественности.',
        image: 'livingPhoto/3.webp',
        reverse: false
    },
    {
        title: 'Превращение фото в короткое видео',
        description: 'Вы загружаете изображение — ИИ создаёт короткий клип с плавной анимацией элементов, готовый для публикации в Reels, TikTok или Stories.',
        image: 'livingPhoto/4.webp',
        reverse: true
    },
    {
        title: 'Стилизация движений под эмоции',
        description: 'Выбираете настроение: радость, динамику, спокойствие — ИИ подбирает стиль анимации, который усиливает эмоциональное восприятие фото.',
        image: 'livingPhoto/5.webp',
        reverse: false
    }
]

const LandingLivingPhoto = () => {
    const { t } = useTranslation();
    return (
        <div className="min-h-screen bg-background-primary">
            <Helmet>
                <title>{t("seo.livingPhoto.title")}</title>
                <meta name="description" content={t("seo.livingPhoto.description")} />
                <meta name="keywords" content={t("seo.livingPhoto.keywords")} />
                <meta property="og:title" content={t("seo.livingPhoto.title")} />
                <meta property="og:description" content={t("seo.livingPhoto.description")} />
                <meta property="og:type" content="website" />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={t("seo.livingPhoto.title")} />
                <meta name="twitter:description" content={t("seo.livingPhoto.description")} />
            </Helmet>
            <Header />
            <Hero title={t("landings.livingPhoto.title")} titleHighlight={t("landings.livingPhoto.titleHighlight")} subtitle={t("landings.livingPhoto.subtitle")} secondaryImage='/heroImages/livePhoto/landingHero2.png' topSecondaryImage='/heroImages/livePhoto/landingHero3.png' bottomSecondaryImage='/heroImages/livePhoto/landingHero4.png'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingLivingPhoto;

import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import { useTranslation } from "next-i18next";

const features = [
    {
        title: 'Автоматическое создание продающих обложек',
        description: 'ИИ берёт фото вашего товара и мгновенно превращает его в яркую обложку с чистым фоном, выгодным ракурсом и правильным освещением, чтобы привлечь максимум внимания на витрине маркетплейса.',
        image: 'marketplace/1.webp',
        reverse: false
    },
    {
        title: 'Стилизация под требования разных площадок',
        description: 'Каждый маркетплейс имеет свои стандарты — фон, размер, пропорции. ИИ автоматически подгоняет изображения под OZON, Wildberries, Amazon или Etsy, чтобы загружать их без правок и отказов модерации.',
        image: 'marketplace/2.webp',
        reverse: true
    },
    {
        title: 'Генерация 3D-рендеров из фото',
        description: 'ИИ превращает обычное фото в реалистичный 3D-рендер, позволяя показать товар с разных ракурсов, даже если у вас нет профессиональной фотосъёмки.',
        image: 'marketplace/3.webp',
        reverse: false
    },
    {
        title: 'Создание креативных баннеров и акций',
        description: 'Вы описываете акцию — ИИ автоматически добавляет привлекательный фон, текст, элементы дизайна и компоновку, чтобы обложка сразу передавала ценность предложения.',
        image: 'marketplace/4.webp',
        reverse: true
    },
    {
        title: 'Серии фото в едином стиле',
        description: 'ИИ создаёт целую серию изображений для карточки товара: обложка, дополнительные фото, инфографика, фото в интерьере, фото с моделью — всё в одном стиле, чтобы карточка выглядела цельно и профессионально.',
        image: 'marketplace/5.webp',
        reverse: false
    }
]

const LandingMarketplace = () => {
    const { t } = useTranslation();
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title={t("landings.marketplace.title")} titleHighlight={t("landings.marketplace.titleHighlight")} subtitle={t("landings.marketplace.subtitle")} secondaryImage='/heroImages/marketplace/landingHero2.png' topSecondaryImage='/heroImages/marketplace/landingHero3.png' bottomSecondaryImage='/heroImages/marketplace/landingHero4.png'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingMarketplace;

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Coins, Zap, Crown, Star, Image, Video } from 'lucide-react';
import Header from '@/components/landing/Header';
import Footer from '@/components/landing/Footer';
import { useTranslation } from 'react-i18next';
import SharedLayout from "@/components/SharedLayout";
import {useMutation} from "@tanstack/react-query";
import {api, mutationKeys} from "@/lib/api";
import {usePBContext} from "@/context/PocketbaseContext";
import {useIsMobile} from "@/hooks/use-mobile";
import {safeYMGoal} from "@/lib/utils";

const Pricing = () => {
  const { t } = useTranslation();
  const { pb } = usePBContext();
  const isMobile = useIsMobile();

  // Token pricing tiers
  const tokenPackages = [
    {
      name: t('pricing.tokenPackages.packages.small.name'),
      tokens: 100,
      priceNumeric: "300",
      price: t('pricing.tokenPackages.packages.small.price'),
      pricePerToken: t('pricing.tokenPackages.packages.small.pricePerToken'),
      description: t('pricing.tokenPackages.packages.small.description'),
      icon: Coins,
      popular: false,
      savings: null,
      buttonText: t('pricing.tokenPackages.packages.small.buttonText')
    },
    {
      name: t('pricing.tokenPackages.packages.medium.name'),
      tokens: 600,
      priceNumeric: "1300",
      price: t('pricing.tokenPackages.packages.medium.price'),
      pricePerToken: t('pricing.tokenPackages.packages.medium.pricePerToken'),
      description: t('pricing.tokenPackages.packages.medium.description'),
      icon: Zap,
      popular: true,
      savings: t('pricing.tokenPackages.packages.medium.savings'),
      buttonText: t('pricing.tokenPackages.packages.medium.buttonText')
    },
    {
      name: t('pricing.tokenPackages.packages.large.name'),
      tokens: 1000,
      priceNumeric: "2000",
      price: t('pricing.tokenPackages.packages.large.price'),
      pricePerToken: t('pricing.tokenPackages.packages.large.pricePerToken'),
      description: t('pricing.tokenPackages.packages.large.description'),
      icon: Crown,
      popular: false,
      savings: t('pricing.tokenPackages.packages.large.savings'),
      buttonText: t('pricing.tokenPackages.packages.large.buttonText')
    }
  ];

  // Per-model token costs based on actual database prices
  const modelCosts = {
    image: [
      {
        name: t('pricing.modelCosts.imageModels.models.seedream3.name'),
        id: 'seedream-3',
        cost: t('pricing.modelCosts.imageModels.models.seedream3.cost'),
        description: t('pricing.modelCosts.imageModels.models.seedream3.description'),
        pricing: t('pricing.modelCosts.imageModels.models.seedream3.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.imagen4Ultra.name'),
        id: 'imagen-4-ultra',
        cost: t('pricing.modelCosts.imageModels.models.imagen4Ultra.cost'),
        description: t('pricing.modelCosts.imageModels.models.imagen4Ultra.description'),
        pricing: t('pricing.modelCosts.imageModels.models.imagen4Ultra.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.fluxKontextPro.name'),
        id: 'flux-kontext-pro',
        cost: t('pricing.modelCosts.imageModels.models.fluxKontextPro.cost'),
        description: t('pricing.modelCosts.imageModels.models.fluxKontextPro.description'),
        pricing: t('pricing.modelCosts.imageModels.models.fluxKontextPro.pricing')
      }
    ],
    video: [
      {
        name: t('pricing.modelCosts.videoModels.models.seedanceLite.name'),
        id: 'seedance-lite',
        variants: [
          { resolution: '480p', cost: 2, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.480p') },
          { resolution: '1080p', cost: 4, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedanceLite.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.seedancePro.name'),
        id: 'seedance-pro',
        variants: [
          { resolution: '480p', cost: 3, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.480p') },
          { resolution: '1080p', cost: 15, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedancePro.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.hunyuan.name'),
        id: 'hunyuan',
        cost: t('pricing.modelCosts.videoModels.models.hunyuan.cost'),
        description: t('pricing.modelCosts.videoModels.models.hunyuan.description'),
        pricing: t('pricing.modelCosts.videoModels.models.hunyuan.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo2.name'),
        id: 'veo2',
        cost: t('pricing.modelCosts.videoModels.models.veo2.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo2.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo2.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo3.name'),
        id: 'veo3',
        cost: t('pricing.modelCosts.videoModels.models.veo3.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo3.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo3.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo3-fast.name'),
        id: 'veo3-fast',
        cost: t('pricing.modelCosts.videoModels.models.veo3-fast.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo3-fast.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo3-fast.pricing')
      }
    ]
  };

  const {
    mutate: createPayment,
    isPending: isCreatingPayment,
  } = useMutation({
    mutationKey: mutationKeys.createPayment,
    mutationFn: (request: {amount: string; referral?: string}) => {
      safeYMGoal('create_payment');
      return api.createPayment(pb, request);
    },
    onSuccess: (response) => {
      console.log(response);
      if (isMobile) {
        window.location.href = response.url;
      } else {
        window.open(response.url, '_blank');
      }
    },
    onError: (error) => {
      console.error('Payment creation error:', error);
    },
  });

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
            {t('pricing.title')}{' '}
            <span className="text-[var(--primary-blue)]">{t('pricing.titleHighlight')}</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('pricing.subtitle')}
          </p>
        </div>

        {/* Token Packages */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto mb-16">
          {tokenPackages.map((pkg, index) => (
            <Card
              key={index}
              className={`relative border transition-all duration-300 hover:shadow-lg rounded-xl ${
                pkg.popular
                  ? 'border border-blue-400 hover:border-gray-300 shadow-lg scale-105'
                  : 'border border-gray-200 hover:border-gray-300'
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-7 md:-top-4 left-1/2 transform -translate-x-1/2 text-center">
                  <div className="bg-blue-600 text-white px-2 py-2 rounded-full text-xs whitespace-nowrap font-semibold flex items-center gap-1">
                    <Star className="w-4 h-4 fill-current" />
                    {t('pricing.popular')}
                  </div>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    pkg.popular
                      ? 'bg-[var(--primary-blue)]'
                      : 'bg-gray-100'
                  }`}>
                    <pkg.icon className={`w-6 h-6 ${pkg.popular ? 'text-white' : 'text-gray-600'}`} />
                  </div>
                </div>
                <CardTitle className="text-xl text-gray-900">{pkg.name}</CardTitle>
                <p className="text-gray-600 text-sm">{pkg.description}</p>
              </CardHeader>

              <CardContent className="text-center">
                <div className="mb-4">
                  <div className="text-3xl font-bold text-gray-900 mb-1">{pkg.price}</div>
                  <div className="text-sm text-gray-500">
                    {pkg.pricePerToken}
                    {pkg.savings && (
                      <span className="block text-green-600 font-semibold mt-1">
                        {pkg.savings}
                      </span>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => createPayment({amount: pkg.priceNumeric, referral: localStorage.getItem('referral')})}
                  disabled={isCreatingPayment}
                  className={`w-full ${
                    pkg.popular
                      ? 'bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded-xl font-semibold text-white'
                      : 'border-2 border-blue-600 bg-white px-4 py-2 rounded-xl font-semibold text-blue-500'
                  }`}
                >
                  {pkg.buttonText}
                </button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Model Pricing Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4 text-gray-900">
            {t('pricing.modelCosts.title')} <span className="text-[var(--primary-blue)]">{t('pricing.modelCosts.titleHighlight')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('pricing.modelCosts.subtitle')}
          </p>
        </div>

        {/* Image Models */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-8">
            <Image className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
            <h3 className="text-2xl font-bold text-gray-900">{t('pricing.modelCosts.imageModels.title')}</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {modelCosts.image.map((model, index) => (
              <Card key={model.id} className="border border-gray-200 shadow-sm">
                <CardContent className="p-6 text-center">
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">{model.name}</h4>
                  <div className="text-2xl font-bold text-[var(--primary-blue)] mb-2">
                    {model.cost}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                  <p className="text-xs text-gray-500">{model.pricing}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Video Models */}
        <div className="mb-12">
          <div className="flex items-center justify-center mb-8">
            <Video className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
            <h3 className="text-2xl font-bold text-gray-900">{t('pricing.modelCosts.videoModels.title')}</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {modelCosts.video.map((model, index) => (
              <Card key={model.id} className="border border-gray-200 shadow-sm rounded-xl">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">{model.name}</h4>
                    <p className="text-sm text-gray-600">{model.description}</p>
                  </div>

                  {model.variants ? (
                    // Models with resolution variants (Seedance)
                    <div className="space-y-3">
                      {model.variants.map((variant, variantIndex) => (
                        <div key={variantIndex} className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium text-gray-900">{variant.resolution}</span>
                            <span className="text-lg font-bold text-[var(--primary-blue)]">
                              {variant.cost}
                            </span>
                          </div>
                          <p className="text-xs text-gray-600">{variant.description}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    // Models with fixed pricing
                    <div className="text-center mt-12">
                      <div className="text-2xl font-bold text-[var(--primary-blue)] mb-2">
                        {model.cost}
                      </div>
                      <p className="text-xs text-gray-600">{model.pricing}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Pricing Examples Section */}
        <div className="mb-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">
              {t('pricing.examples.title')} <span className="text-[var(--primary-blue)]">{t('pricing.examples.titleHighlight')}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('pricing.examples.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {/* Image Examples */}
            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.singleImage.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">{t('pricing.examples.categories.singleImage.items.fluxDev')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.singleImage.items.seedream3')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.singleImage.items.imagen4Ultra')}</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.batchGeneration.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">{t('pricing.examples.categories.batchGeneration.items.fluxDev')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.batchGeneration.items.seedream3')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.batchGeneration.items.imagen4Ultra')}</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.shortVideos.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">{t('pricing.examples.categories.shortVideos.items.seedanceLite480')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.shortVideos.items.seedancePro480')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.shortVideos.items.veo2')}</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.hdVideos.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">{t('pricing.examples.categories.hdVideos.items.seedanceLite1080')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.hdVideos.items.seedancePro1080')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.hdVideos.items.hunyuan')}</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.premiumVideos.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-gray-600">{t('pricing.examples.categories.premiumVideos.items.veo3')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.premiumVideos.items.veo2Long')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.premiumVideos.items.seedanceProLong')}</div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Coins className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">{t('pricing.examples.categories.costComparison.title')}</h3>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="text-[var(--primary-blue)] mb-2">{t('pricing.examples.categories.costComparison.subtitle')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.costComparison.items.fluxDevImages')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.costComparison.items.seedanceLiteVideos')}</div>
                  <div className="text-gray-600">{t('pricing.examples.categories.costComparison.items.hunyuanVideos')}</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            {t('pricing.faq.title')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.tokensExpire.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.tokensExpire.answer')}
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.videoCosts.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.videoCosts.answer')}
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.generationFails.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.generationFails.answer')}
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.modelPrices.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.modelPrices.answer')}
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.bulkDiscounts.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.bulkDiscounts.answer')}
                </p>
              </CardContent>
            </Card>

            <Card className="border border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-3">{t('pricing.faq.items.trackUsage.question')}</h3>
                <p className="text-gray-600">
                  {t('pricing.faq.items.trackUsage.answer')}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SharedLayout>
  );
};

export default Pricing;

{"common": {"loading": "Загрузка...", "error": "Ошибка", "success": "Успех", "cancel": "Отмена", "save": "Сохранить", "delete": "Удалить", "edit": "Редактировать", "close": "Закрыть", "back": "Назад", "next": "Далее", "previous": "Предыдущий", "submit": "Отправить", "search": "Поиск", "filter": "Фильтр", "sort": "Сортировка", "all": "Все", "none": "Нет", "yes": "Да", "no": "Нет"}, "header": {"brand": "Холст.ИИ", "navigation": {"features": "Возможности", "gallery": "Галерея", "pricing": "Цены", "faq": "FAQ"}, "getStarted": "Начать"}, "hero": {"title": "Создавайте", "titleHighlight": "Потрясающие изображения и видео", "titleSuffix": "", "subtitle": "Превращайте текстовые описания в уникальные изображения и видео без VPN и ограничений. Наши ИИ модели — ваш новый инструмент для безграничного творчества.", "startCreating": "Начать творить", "viewExamples": "Галерея", "supportedModels": "Поддерживаемые AI модели", "noCardRequired": "Без карты", "noVPNRequired": "Без VPN", "noSubscription": "Без подписки", "stats": {"value1": "30 сек+", "label1": "Генерация изображения", "value2": "90 сек+", "label2": "Генерация видео", "value3": "9", "label3": "AI моделей"}}, "landings": {"smm": {"pageName": "Создание контента для SMM", "title": "Создавайте", "titleHighlight": "Потрясающий контент для соцсетей", "subtitle": "Упростите и ускорьте процесс создания контента для постов в социальных сетях. Создавайте яркий визуал, вдохновляющий и привлекающий внимание."}, "marketplace": {"pageName": "Создание обложек для маркетплейсов", "title": "Создавайте", "titleHighlight": "Визуалы для маркетплейсов за минуты", "subtitle": "Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео, соответствующие стандартам OZON, Wildberries, Amazon и других площадок. Продающие обложки, 3D-рендеры, баннеры для акций — без фотостудии и долгих согласований."}, "inspiration": {"pageName": "Поиск вдохновения", "title": "Вдохновение", "titleHighlight": "на расстоянии одного клика", "subtitle": "Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции, которые помогут запустить креативный процесс и найти новое направление для ваших проектов."}, "livingPhoto": {"pageName": "Оживление фотографий", "title": "Оживите свои фотографии", "titleHighlight": "C помощью ИИ", "subtitle": "Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд. Идеально подходит для соцсетей, презентаций и рекламы — оживите свои фото без сложных программ и студий."}, "influencer": {"pageName": "Создание инфлюэнсеров", "title": "Создайте", "titleHighlight": "Виртуального инфлюэнсера за минуты", "subtitle": "Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты, публиковать посты и взаимодействовать с аудиторией. Идеально для Instagram, TikTok, YouTube или других платформ."}}, "features": {"title": "Инструменты для", "secondTitle": "Наши преимущества", "titleHighlight": "нового поколения творцов", "subtitle": "Откройте полный потенциал AI для своих проектов. Мы объединили скорость, контроль и качество на одной платформе.", "items": {"aiPowered": {"title": "Без VPN", "description": "Больше не требуется VPN для доступа к передовым AI моделям. Наш сервис доступен для всех пользователей без ограничений."}, "lightningFast": {"title": "Молниеносная Скорость", "description": "Получайте готовые работы за секунды, а не часы. Наша оптимизированная инфраструктура создана для мгновенной реакции на ваши идеи."}, "artisticControl": {"title": "Художественный Контроль", "description": "Управляйте каждым аспектом: от стиля и ракурса до цветовой палитры. Добивайтесь полного соответствия вашего творческому видению с помощью точных настроек."}, "multipleFormats": {"title": "Множество Форматов", "description": "Экспортируйте результат в любом необходимом формате. От 4K-видео до изображений для социальных сетей — ваш контент всегда готов к публикации."}, "commercialLicense": {"title": "Коммерческая Лицензия", "description": "Все созданные вами произведения принадлежат вам. Используйте их в коммерческих проектах, маркетинге и рекламе без каких-либо ограничений."}, "advancedModels": {"title": "Продвинутые Модели", "description": "Получите доступ к эксклюзивным AI-моделям, обученным для разных стилей и задач. Постоянно обновляемые алгоритмы для самых передовых результатов."}}}, "gallery": {"title": "Создано с помощью", "titleHighlight": "Холст.ИИ", "subtitle": "Посмотрите, что создают другие пользователи, или найдите вдохновение для своего следующего шедевра.", "tabs": {"images": "Изображения", "videos": "Видео"}, "imageAlt": "AI Созданное Изображение", "videoAlt": "AI Созданное Видео", "sampleImages": [{"prompt": "A cinematic, photorealistic medium shot capturing the nostalgic warmth of a mid-2000s indie film. The focus is a young woman with a sleek, straight bob haircut in cool platinum white with freckled skin, looking directly and intently into the camera lens with a knowing smirk, her head is looking up slightly. She wears an oversized band t-shirt that says \"Seedream 3.0 on Replicate\" in huge stylized text over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight creating lens flare and illuminating dust motes in the air. The background shows a blurred outdoor urban setting with graffiti-covered walls (the graffiti says \"seedream\" in stylized graffiti lettering), rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel", "model": "Seedream-3"}, {"prompt": "The photo: Create a cinematic, photorealistic medium shot capturing the nostalgic warmth of a late 90s indie film. The focus is a young woman with brightly dyed pink-gold hair and freckled skin, looking directly and intently into the camera lens with a hopeful yet slightly uncertain smile, she is slightly off-center. She wears an oversized, vintage band t-shirt that says \"Replicate\" (slightly worn) over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight streaming through a slightly dusty window, creating lens flare and illuminating dust motes in the air. The background shows a blurred, cluttered bedroom with posters on the wall and fairy lights, rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel", "model": "Imagen 4 Ultra"}, {"prompt": "Using this <PERSON> style, a panda astronaut riding a unicorn", "model": "Flux Kontext Pro"}, {"prompt": "black forest gateau cake spelling out the words \"FLUX DEV\", tasty, food photography, dynamic shot", "model": "Flux Dev"}], "sampleVideos": [{"prompt": "Bearded ancient philosopher in classical robes teaching wisdom to students in a marble garden setting, speaking with modern youthful language and expressions. The teacher gestures while sharing philosophical concepts using contemporary slang. Students in period clothing listen attentively. Warm natural lighting, classical architecture background, blending timeless wisdom with current speech pattern", "model": "Google Veo3"}, {"prompt": "The sun rises slowly between tall buildings. [Ground-level follow shot] Bicycle tires roll over a dew-covered street at dawn. The cyclist passes through dappled light under a bridge as the entire city gradually wakes up.", "model": "Seedance Pro"}, {"prompt": "A woman walks in the park", "model": "Seedance Lite"}, {"prompt": "A cat walks on the grass, realistic style", "model": "Huyuan Video"}]}, "faq": {"title": "Часто Задаваемые", "titleHighlight": "Вопросы", "subtitle": "В<PERSON><PERSON>, что нужно знать о  Холст.ИИ  — нашей платформе для создания контента с помощью искусственного интеллекта.", "items": {"whatIsAi": {"question": "Что такое AI-генерация контента?", "answer": "Это технология, которая использует искусственный интеллект для создания уникальных изображений, видео и других визуальных материалов на основе ваших текстовых описаний. Вы описываете идею словами —  Холст.ИИ  превращает её в готовый контент."}, "howLong": {"question": "Сколько времени занимает генерация контента?", "answer": "Большинство изображений генерируются за 30-60 секунд. Время создания видео зависит от его длины и сложности, но наша платформа оптимизирована, что дает максимальную скорость."}, "fileFormats": {"question": "Какие форматы файлов поддерживаются?", "answer": "Мы поддерживаем все популярные форматы: PNG, WebP и JPEG для изображений, MP4, WebM для видео. Вы можете выбрать высокое разрешение для печати или оптимизированные версии для соцсетей."}, "commercial": {"question": "Могу ли я использовать созданный контент в коммерческих целях?", "answer": "Да, абсолютно. С нашей коммерческой лицензией вы получаете полные права на использование созданного контента в любых целях, включая рекламу, маркетинг и продажу продуктов."}, "billing": {"question": "Как происходит оплата?", "answer": "Вы покупаете кредиты, которые далее расходуете в процессе генерации контента. Оплату можно производить картой или по купону."}, "dataPrivacy": {"question": "Что происходит с моими данными и созданным контентом?", "answer": "Ваши данные и созданный контент полностью конфиденциальны. Они принадлежат только вам и хранятся на защищенных серверах. Мы никогда не используем их без вашего разрешения."}, "refunds": {"question": "Какая у вас политика возврата средств?", "answer": "Мы стремимся к тому, чтобы вы были довольны результатом. Если у вас возникли проблемы, свяжитесь с нашей службой поддержки, и мы рассмотрим вашу ситуацию индивидуально. Подробнее — в наших Условиях Использования."}}}, "footer": {"brand": "Холст.ИИ", "description": "Превратите свое творческое видение в реальность с помощью наших передовых AI технологий. Генерируйте потрясающие визуалы и видео за секунды.", "sections": {"product": {"title": "Продукт", "features": "Возможности", "gallery": "Галерея", "pricing": "Цены", "api": "API"}, "company": {"title": "Компания", "aboutUs": "О Нас", "blog": "Блог", "careers": "Карьера", "press": "Пресса"}, "support": {"title": "Поддержка", "helpCenter": "Центр Помощи", "faq": "FAQ", "contact": "Контакты", "status": "Статус"}, "legal": {"title": "Правовая Информация", "privacyPolicy": "Политика Конфиденциальности", "termsOfService": "Условия Использования", "cookiePolicy": "Политика Cookies", "gdpr": "GDPR"}}, "copyright": "© 2025 Холст.ИИ. ИП Ступаков Ю.В. ИНН 773000356687. Все права защищены."}, "login": {"title": "Добро пожаловать", "subtitle": "Войдите в свой аккаунт Холст.ИИ", "continueWithGoogle": "Продолжить с Google", "continueWithYandex": "Продолжить с Яндекс", "continueWithVK": "Продолжить с ВКонтакте"}, "sidebar": {"brand": "Холст.ИИ", "navigation": {"create": "Создать", "history": "История", "pricing": "Пополнить", "support": "Поддержка", "profile": "Профиль"}, "user": {"defaultName": "Пользователь", "credits": "кредиты", "profileAlt": "Профиль Пользователя"}}, "studio": {"title": "Создавайте с AI", "description": "Превратите свое воображение в реальность"}, "profile": {"title": "Профиль", "description": "Управляйте своим аккаунтом и просматривайте свои творения", "sections": {"profileInformation": "Информация Профиля", "credits": "Кредиты", "referral": "Реферальная программа"}, "fields": {"fullName": "Полное Имя", "email": "Email", "username": "Имя Пользователя"}, "buttons": {"saveChanges": "Сохранить Изменения", "logout": "Выйти", "paymentComingSoon": "Оплата скоро...", "topup": "Пополнить"}, "stats": {"creditsRemaining": "Кредитов осталось"}, "congratulations": {"title": "Платеж успешен! 🎉", "message": "Ваш аккаунт пополнен на {{amount}} кредитов!", "subtitle": "Спасибо за покупку. Теперь вы можете создавать потрясающий контент с ИИ!", "continueButton": "Продолжить создание"}, "referral": {"title": "Реферальная программа", "description": "Приглашайте друзей и зарабатывайте кредиты вместе!", "offer": "Получайте 50% от кредитов, которые покупает ваш друг, и 25 кредитов за каждого зарегистрированного друга", "linkLabel": "Ваша реферальная ссылка", "copyButton": "Копировать ссылку", "copySuccess": "Реферальная ссылка скопирована!", "copySuccessDescription": "Поделитесь этой ссылкой с друзьями, чтобы начать зарабатывать кредиты.", "copyError": "Ошибка копирования", "copyErrorDescription": "Не удалось скопировать реферальную ссылку. Попробуйте еще раз."}, "recentGenerations": {"title": "Недавние Генерации", "viewAll": "Посмотреть Все", "type": "Тип", "title_field": "Название", "date": "Дата", "status": "Статус", "completed": "Завершено", "processing": "Обработка"}}, "pricing": {"title": "Простые", "titleHighlight": "цены", "subtitle": "Платите только за то, что используете. Никаких подписок, никаких скрытых комиссий. Покупайте кредиты и используйте их для всех наших AI моделей.", "popular": "Самый популярный", "tokenPackages": {"title": "Пакеты Кредитов", "subtitle": "Выберите количество кредитов, которое подходит для ваших потребностей", "packages": {"small": {"name": "100 Кредитов", "price": "300₽", "pricePerToken": "3₽ за кредит", "description": "Идеально для знакомства с нашими AI моделями", "buttonText": "Купить 100 Кредитов"}, "medium": {"name": "600 Кредитов", "price": "1300₽", "pricePerToken": "2.16₽ за кредит", "description": "Отлично для регулярных творческих проектов", "savings": "Скидка 28%", "buttonText": "Купить 600 Кредитов"}, "large": {"name": "1000 Кредитов", "price": "2000₽", "pricePerToken": "2₽ за кредит", "description": "Лучшее предложение для активных пользователей", "savings": "Скидка 33%", "buttonText": "Купить 1000 Кредитов"}}}, "modelCosts": {"title": "Стоимость", "titleHighlight": "по моделям", "subtitle": "Разные AI модели требуют различных вычислительных ресурсов. Вот сколько кредитов стоит каждая модель за генерацию.", "imageModels": {"title": "Модели Генерации Изображений", "models": {"fluxDev": {"name": "FLUX Dev", "cost": "3 кредита", "description": "Быстрая, высококачественная генерация изображений", "pricing": "3 кредита × количество выходов"}, "seedream3": {"name": "Seedream 3", "cost": "3 кредита", "description": "Поддержка нативного разрешения 2K", "pricing": "3 кредита за изображение"}, "imagen4Ultra": {"name": "Imagen 4 Ultra", "cost": "6 кредитов", "description": "Ультра высокое разрешение изображений", "pricing": "6 кредитов за изображение"}, "fluxKontextPro": {"name": "FLUX Kontext Pro", "cost": "4 кредита", "description": "Профессиональное редактирование изображений", "pricing": "4 кредита за изображение"}}}, "videoModels": {"title": "Модели Генерации Видео", "models": {"seedanceLite": {"name": "Seedance Lite", "description": "Быстрая генерация видео до 10 секунд", "variants": {"480p": "2 кредита × длительность (секунды)", "1080p": "4 кредита × длительность (секунды)"}}, "seedancePro": {"name": "Seedance Pro", "description": "Качественная генерация видео до 10 секунд", "variants": {"480p": "3 кредита × длительность (секунды)", "1080p": "15 кредитов × длительность (секунды)"}}, "hunyuan": {"name": "Hunyuan Video", "cost": "250 кредитов", "description": "Высококачественная генерация видео", "pricing": "250 кредитов за видео"}, "veo2": {"name": "Veo 2", "cost": "50 кредитов", "description": "Продвинутые возможности видео", "pricing": "50 кредитов × длительность (секунды)"}, "veo3": {"name": "Veo 3", "cost": "75 кредитов", "description": "Новейшие технологии генерации видео", "pricing": "75 кредитов × 8 секунд = 600 кредитов за видео"}, "veo3-fast": {"name": "Veo 3 Fast", "cost": "40 кредитов", "description": "Более быстрая и дешевая версия Veo3", "pricing": "40 кредитов × 8 секунд = 320 кредитов за видео"}}}}, "examples": {"title": "Примеры", "titleHighlight": "Цен", "subtitle": "Посмотрите, сколько стоят обычные задачи генерации с нашей кредитной системой.", "categories": {"singleImage": {"title": "Одно Изображение", "items": {"fluxDev": "FLUX Dev (1 изображение): 3 кредита", "seedream3": "Seedream 3 (1 изображение): 3 кредита", "imagen4Ultra": "Imagen 4 Ultra (1 изображение): 6 кредитов"}}, "batchGeneration": {"title": "Пакетная Генерация", "items": {"fluxDev": "FLUX Dev (4 изображения): 12 кредитов", "seedream3": "Seedream 3 (4 изображения): 12 кредитов", "imagen4Ultra": "Imagen 4 Ultra (4 изображения): 24 кредита"}}, "shortVideos": {"title": "Короткие Видео", "items": {"seedanceLite480": "Seedance Lite 480p (5с): 10 кредитов", "seedancePro480": "Seedance Pro 480p (5с): 15 кредитов", "veo2": "Veo 2 (5с): 250 кредитов"}}, "hdVideos": {"title": "HD Видео", "items": {"seedanceLite1080": "Seedance Lite 1080p (5с): 20 кредитов", "seedancePro1080": "Seedance Pro 1080p (5с): 75 кредитов", "hunyuan": "Hunyuan Video: 250 кредитов"}}, "premiumVideos": {"title": "Премиум Видео", "items": {"veo3": "Veo 3 (8с фиксированно): 600 кредитов", "veo2Long": "Veo 2 (10с): 500 кредитов", "seedanceProLong": "Seedance Pro 1080p (10с): 150 кредитов"}}, "costComparison": {"title": "Сравнение Стоимости", "subtitle": "С 600 кредитами (1300₽):", "items": {"fluxDevImages": "~200 изображений Seedream 3: 6.5₽ за каждое", "seedanceLiteVideos": "~30 видео Seedance Lite (5с): 43₽ за каждое", "hunyuanVideos": "~2 видео Hunyuan: 650₽ за каждое"}}}}, "faq": {"title": "Часто Задаваемые Вопросы", "items": {"tokensExpire": {"question": "Истекают ли кредиты?", "answer": "Нет, ваши кредиты никогда не истекают. Купите их один раз и используйте, когда захотите создавать контент"}, "videoCosts": {"question": "Как рассчитывается стоимость видео?", "answer": " Стоимость видео зависит от модели, разрешения и длительности. Например, модели Seedance берут плату за секунду, а Veo 3 имеет фиксированную продолжительность — 8 секунд"}, "generationFails": {"question": "Что происходит если генерация не удается?", "answer": "Если генерация не удается из-за ошибки нашей системы, ваши кредиты будут автоматически возвращены на ваш счет."}, "modelPrices": {"question": "Почему у моделей разные цены?", "answer": "Разные AI модели требуют различных вычислительных ресурсов. Более продвинутые модели, такие как Veo 3 и Hunyuan, стоят дороже из-за их превосходного качества и расширенных возможностей"}, "bulkDiscounts": {"question": "Можно ли получить скидки за объем?", "answer": "Да! Большие пакеты кредитов предлагают лучшие цены за кредит. Пакет из 1000 кредитов экономит вам 33% по сравнению с пакетом из 100 кредитов."}, "trackUsage": {"question": "Как отслеживать использование кредитов?", "answer": "Ваш текущий баланс кредитов отображается в вашем профиле, и каждая генерация показывает точную стоимость в кредитах перед подтверждением."}}}, "cta": {"subtitle": "Готовы начать создавать? Зарегистрируйтесь и начните работу с нашими AI моделями.", "buttonText": "Начать Сейчас"}}, "videoGenerator": {"tabs": {"video": "Видео", "image": "Изображение"}, "selectModel": "Выбрать Модель", "promptLabel": "Опишите что вы хотите создать", "promptPlaceholder": "Спокойный горный пейзаж на рассвете с туманом катящимся по долинам, кинематографическое освещение, ультра-детализированный...", "translatePrompt": {"label": "Перевести запрос на английский"}, "models": {"hunyuan": {"name": "Hunyuan Video", "description": "Современная модель генерации видео из текста, способная создавать высококачественные видео с реалистичным движением из текстовых описаний"}, "veo2": {"name": "Veo 2", "description": "Современная модель генерации видео. Veo 2 может точно следовать простым и сложным инструкциям, убедительно имитируя физику реального мира и широкий спектр визуальных стилей."}, "veo3": {"name": "Veo 3", "description": "Флагманская модель Google Veo 3 для генерации видео из текста с возможностями генерации аудио"}, "veo3-fast": {"name": "Veo 3 Fast", "description": "Более быстрая и дешевая версия Google Veo 3 с возможностями генерации аудио"}, "seedance-pro": {"name": "Seedance Pro", "description": "Профессиональная версия Seedance, которая предлагает поддержку генерации видео из текста и изображения для видео 5с или 10с, в разрешении 480p и 1080p"}, "seedance-lite": {"name": "Seedance Lite", "description": "Модель генерации видео, которая предлагает поддержку генерации видео из текста и изображения для видео 5с или 10с, в разрешении 480p и 720p"}, "flux-dev": {"name": "FLUX Dev", "description": "Трансформер с 12 миллиардами параметров, способный генерировать изображения из текстовых описаний"}, "flux-kontext-pro": {"name": "FLUX Kontext Pro", "description": "Современная модель редактирования изображений на основе текста, которая обеспечивает высококачественные результаты с отличным следованием промптам и стабильными результатами для преобразования изображений через естественный язык"}, "imagen-4-ultra": {"name": "Imagen 4 Ultra", "description": "Используйте эту ультра-версию Imagen 4, когда качество важнее скорости и стоимости"}, "seedream-3": {"name": "Seedream 3", "description": "Модель генерации изображений из текста с поддержкой нативной генерации изображений высокого разрешения (2K)"}, "qwen-image": {"name": "<PERSON><PERSON>", "description": "Модель генерации изображений с продвинутыми возможностями рендеринга текста и точного редактирования изображений"}}, "parameters": {"options": {"video_length": {"49": "49 кадров (~2 секунды)", "129": "129 кадров (~5 секунд)"}, "safety_filter_level": {"block_low_and_above": "Строгий", "block_medium_and_above": "Умеренный", "block_only_high": "Свободный"}, "output_format": {"jpg": "JPEG", "png": "PNG"}, "aspect_ratio": {"1": "1:1 (К<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "9": "16:9 (Ал<PERSON>б<PERSON><PERSON>ная)", "16": "9:16 (Пор<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "3": "4:3 (Ста<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "4": "3:4 (Порт<PERSON><PERSON><PERSON><PERSON><PERSON>)"}}, "labels": {"width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "video_length": "<PERSON><PERSON><PERSON><PERSON> видео", "infer_steps": "Шаги генерации", "embedded_guidance_scale": "Шкала направления", "fps": "FPS", "seed": "Сид", "image": "Входное изображение (опционально)", "aspect_ratio": "Соотношение сторон", "duration": "Длительность", "enhance_prompt": "Улучшить запрос", "negative_prompt": "Негативный запрос", "resolution": "Разрешение", "camera_fixed": "Фиксированная камера", "num_outputs": "Количество изображений", "num_inference_steps": "Шаги вывода", "guidance": "Направление", "go_fast": "Быстро", "megapixels": "Мегапиксели", "output_format": "Формат вывода", "output_quality": "Качество вывода", "disable_safety_checker": "Отключить проверку безопасности", "prompt_strength": "Сила запроса", "input_image": "Входное изображение (опционально)", "safety_tolerance": "Толерантность безопасности", "safety_filter_level": "Уровень фильтра безопасности", "size": "Размер изображения", "guidance_scale": "Шкала направления"}, "descriptions": {"width": "Ширина видео в пикселях (должна быть кратна 16)", "height": "Высота видео в пикселях (должна быть кратна 16)", "video_length": "Количество кадров для генерации (должно быть 4k+1, например: 49 или 129)", "infer_steps": "Количество шагов генерации. Большие значения улучшают качество, но требуют больше времени.", "embedded_guidance_scale": "Контролирует, насколько точно модель следует запросу. Большие значения = больше соответствия.", "fps": "Кадров в секунду выходного видео", "seed": "Случайное число для воспроизводимой генерации. Оставьте пустым для случайного значения.", "image": "Входное изображение для начала генерации. Идеальные изображения 16:9 или 9:16 и 1280x720 или 720x1280, в зависимости от выбранного соотношения сторон.", "aspect_ratio": "Соотношение сторон видео", "duration": "Длительность видео в секундах", "enhance_prompt": "Использовать ИИ для улучшения вашего запроса, чтобы получить лучший результат", "negative_prompt": "Описание того, чего следует избегать", "resolution": "Разрешение видео", "camera_fixed": "Зафиксировать ли позицию камеры", "num_outputs": "Количество результатов", "num_inference_steps": "Количество шагов генерации. Рекомендуемый диапазон 28-50. Меньшие значения быстрее, но хуже качество.", "guidance": "Направление для генерируемого изображения. Меньшие значения могут дать более реалистичные изображения. Хорошие значения: 2, 2.5, 3, 3.5", "go_fast": "Запустить более быструю генерацию с дополнительными оптимизациями", "megapixels": "Приблизительное количество мегапикселей для генерируемого изображения", "output_format": "Формат выходных изображений", "output_quality": "Качество при сохранении выходных изображений, от 0 до 100. 100 - лучшее качество. Не актуально для PNG выходов", "disable_safety_checker": "Отключить проверку безопасности для генерируемых изображений", "prompt_strength": "Сила запроса при использовании img2img. 1.0 соответствует полному разрушению информации в изображении", "input_image": "Изображение для использования в качестве ссылки. Должно быть jpeg, png, gif или webp.", "safety_tolerance": "Толерантность безопасности, 0 - самая строгая, 6 - самая разрешительная. 2 - максимально разрешенная при использовании входных изображений.", "safety_filter_level": "Уровень фильтрации контента безопасности. \"block_low_and_above\" - самый строгий, \"block_medium_and_above\" блокирует некоторые запросы, \"block_only_high\" - самый разрешительный, но некоторые запросы все равно будут заблокированы", "size": "Большие изображения будут иметь самое длинное измерение 2048px. Маленькие изображения будут иметь самое короткое измерение 512px. Обычные изображения всегда будут 1 мегапиксель. Игнорируется, если соотношение сторон пользовательское.", "guidance_scale": "Соответствие запросу. Большие значения = более буквальная интерпретация запроса."}, "placeholders": {"seed": "Оставьте пустым для случайного", "negative_prompt": "например, размытый, низкое качество, искаженный...", "aspect_ratio": "Выберите соотношение сторон", "image_size": "Выберите размер изображения", "output_format": "Выберите формат вывода", "width": "Введите ширину", "height": "Введите высоту", "video_length": "Выберите опцию", "fps": "Введите FPS", "safety_filter_level": "Выберите опцию"}}, "options": {"aspectRatios": {"1:1": "1:1 (К<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "16:9": "16:9 (Ал<PERSON>б<PERSON><PERSON>ная)", "9:16": "9:16 (Пор<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "4:3": "4:3 (Ста<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "3:4": "3:4 (Порт<PERSON><PERSON><PERSON><PERSON><PERSON>)", "21:9": "21:9 (Ульт<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "9:21": "9:21 (Высокая Портретная)", "3:2": "3:2 (Фото)", "2:3": "2:3 (Фото Портретная)", "4:5": "4:5 (Порт<PERSON><PERSON><PERSON><PERSON><PERSON>)", "5:4": "5:4 (Ал<PERSON>б<PERSON><PERSON><PERSON>я)", "2:1": "2:1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "1:2": "1:2 (Высокая)", "match_input_image": "Соответствовать Входному Изображению", "custom": "Пользовательская (указать ширину/высоту)", "49": "49 кадров (~2 секунды)", "129": "129 кадров (~5 секунд)", "block_low_and_above": "Недоступно в этом режиме фильтрации", "block_medium_and_above": "Недоступно в этом режиме фильтрации", "block_only_high": "Недоступно в этом режиме фильтрации", "jpg": "Выберите соотношение сторон", "png": "Выберите соотношение сторон"}, "videoLength": {"49": "49 кадров (~2 секунды)", "129": "129 кадров (~5 секунд)"}, "duration": {"5": "5 секунд", "6": "6 секунд", "7": "7 секунд", "8": "8 секунд", "10": "10 секунд"}, "resolution": {"480p": "480p (Быстрее)", "720p": "720p (Стандартное)", "1080p": "1080p (Высокое Качество)"}, "imageCount": {"1": "1 изображение", "2": "2 изображения", "4": "4 изображения"}, "formats": {"png": "PNG", "jpg": "JPEG", "webp": "WebP (Рекомендуется)"}, "megapixels": {"0.25": "0.25 МП (Быстрее)", "1": "1 МП (Стандартный)"}, "safetyFilterLevel": {"block_low_and_above": "Строгий (Блокировать Низкий и Выше)", "block_medium_and_above": "Умеренный (Блокировать Средний и Выше)", "block_only_high": "Разрешительный (Блокировать Только Высокий)"}, "imageSize": {"small": "Маленький (512px самая короткая)", "regular": "Обычный (1 мегапиксель)", "big": "Большой (2048px самая длинная)"}, "boolean": {"enabled": "Включено", "disabled": "Отключено"}, "fileInput": {"uploaded": "Загружена"}}, "generateButton": "Генерировать", "generating": "Генерация...", "generationFailed": "Генерация не удалась. Попробуйте еще раз.", "uploadSection": {"title": "Загрузите изображение для анимации", "description": "Перетащите или нажмите для выбора", "chooseImage": "Выбрать изображение"}, "activeGenerations": {"title": "Текущие генерации", "videoGeneration": "Генерация видео", "imageGeneration": "Генерация изображения", "generating": "Генерируем ваше {type}... Это может занять несколько секунд.", "generationFailed": "Сгенерировать не удалось. Попробуйте еще раз.", "download": "Скачать", "downloadVideo": "Скачать Видео", "downloadImage": "Скачать Изображение", "generatedImageAlt": "Созданное изображение"}, "modelSpecific": {"seedreamSpecific": "Специфично для Seedream-3", "imagenSpecific": "Специфично для Imagen-4-Ultra", "fluxSpecific": "Специфично для FLUX Kontext Pro", "size": "Размер", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "safetyFilterLevel": "Уровень Фильтра Безопасности", "megapixels": "Мегапиксели", "safetyTolerance": "Толерантность Безопасности", "promptStrength": "Сила Запроса", "fastGeneration": "Быстрая Генерация", "disableSafetyChecker": "Отключить Проверку Безопасности", "outputQuality": "Качество Вывода", "safetyOptions": {"blockMost": "Блокировать Большинство", "blockSome": "Блокировать Некоторые", "blockFew": "Блокировать Немного"}, "toleranceOptions": {"low": "Низкая (1)", "medium": "Средняя (2)", "high": "Высокая (3)", "veryHigh": "Очень Высокая (4)", "maximum": "Максимальная (5)"}}, "statusMessages": {"generating": "Генерируем ваше {{type}}... Это может занять несколько секунд.", "generationFailed": "Генерация не удалась. Ошибка: {{error}}", "browserNotSupported": "Ваш браузер не поддерживает видео тег.", "notEnoughTokens": "Недостаточно кредитов. Требуется: {{amount}} кредитов", "tokensAmount": " {{amount}} кредитов"}, "toastMessages": {"videoGenerationStarted": "Генерация видео началась", "imageGenerationStarted": "Генерация изображения началась", "generationStartedDescription": "Ваше {{type}} генерируется. Вы получите уведомление когда оно будет готово.", "success": "Успех", "error": "Ошибка", "videoGeneratedSuccess": "{{type}} успешно создано!", "generationFailedDescription": "Сгенерировать {{type}} не удалось. Ошибка: {{error}}", "imageUploadFailed": "Ошибка загрузка изображения: {{error}}", "imageUploadFailedUnknownError": "Неизвестная ошибка загрузки изображения", "failedToStart": "Не удалось начать создать {{type}}. Попробуйте еще раз."}, "types": {"video": "видео", "image": "изображение"}, "settings": {"advancedSettings": "Расширенные настройки", "advancedSettingsDescription": "Дополнительные возможности настройки генерации"}, "imageSpecific": {"fluxDevConfig": {"modelName": "FLUX Dev", "description": "Модель с 12 миллиардами параметров, способная генерировать изображения из текстовых описаний", "parameters": {"image": {"label": "Входное изображение (необязательно)", "description": "Входное изображение для режима преобразования изображения в изображение. Соотношение сторон выходного изображения будет соответствовать этому изображению"}, "prompt_strength": {"label": "Сила запроса", "description": "Сила запроса при использовании преобразования изображения в изображение. 1.0 соответствует полному разрушению информации в изображении"}, "num_outputs": {"label": "Количество изображений", "description": "Количество изображений для генерации", "options": {"1": "1 изображение", "2": "2 изображения", "4": "4 изображения"}}, "num_inference_steps": {"label": "Шаги инференции", "description": "Количество шагов шумоподавления. Рекомендуемый диапазон 28-50. Меньшие значения быстрее, но дают более низкое качество."}, "guidance": {"label": "Руководство", "description": "Руководство по сгенерированному изображению. Меньшие значения могут давать более реалистичные изображения. Хорошие значения: 2, 2.5, 3, 3.5"}, "go_fast": {"label": "Быстрый режим", "description": "Создавайте изображение быстрее с дополнительными оптимизациями"}, "megapixels": {"label": "Мегапиксели", "description": "Приблизительное количество мегапикселей для сгенерированного изображения", "options": {"0.25": "0.25 МП (Быстрее)", "1": "1 МП (Стандартно)"}}, "output_quality": {"label": "Качество вывода", "description": "Качество при сохранении выходных изображений, от 0 до 100. 100 - лучшее качество. Неактуально для PNG-выводов"}, "disable_safety_checker": {"label": "Отключить проверку безопасности", "description": "Отключить проверку безопасности для сгенерированных изображений"}}}, "fluxKontextProConfig": {"modelName": "FLUX Kontext Pro", "description": "Передовая модель редактирования изображений на основе текста, которая обеспечивает высококачественные результаты с отличным следованием подсказкам и стабильными результатами для преобразования изображений с помощью естественного языка", "parameters": {"input_image": {"label": "Входное изображение (необязательно)", "description": "Изображение для использования в качестве ссылки. Должно быть jpeg, png, gif или webp."}, "safety_tolerance": {"label": "Допуск безопасности", "description": "Допуск безопасности: 0 - самый строгий, 6 - самый разрешительный. 2 в настоящее время является максимально допустимым при использовании входных изображений."}}}, "imagen4UltraConfig": {"modelName": "Imagen 4 Ultra", "description": "Используйте эту ультра-версию Imagen 4, когда качество важнее скорости и стоимости", "parameters": {"safety_filter_level": {"label": "Уровень фильтра безопасности", "description": "Уровень фильтрации безопасности контента. \"block_low_and_above\" является самым строгим, \"block_medium_and_above\" блокирует некоторые подсказки, \"block_only_high\" является наиболее разрешительным, но некоторые подсказки все равно будут заблокированы", "options": {"block_low_and_above": {"label": "Строгий (блокировать низкий и выше)", "description": "Наиболее ограничительная фильтрация"}, "block_medium_and_above": {"label": "Умеренный (блокировать средний и выше)", "description": "Сбалансированная фильтрация"}, "block_only_high": {"label": "Разрешительный (блокировать только высокий)", "description": "Наименее ограничительная фильтрация"}}}}}, "seedream3Config": {"modelName": "Seedream 3", "description": "Модель преобразования текста в изображение с поддержкой генерации изображений с высоким разрешением (2K)", "parameters": {"size": {"label": "Размер изображения", "description": "Большие изображения будут иметь самую длинную сторону 2048 пикселей. Короткие изображения будут иметь самую короткую сторону 512 пикселей. Обычные изображения всегда будут иметь 1 мегапиксель. Игнорируется, если соотношение сторон пользовательское."}, "width": {"label": "Ши<PERSON><PERSON><PERSON>", "description": "Ши<PERSON>ина изображения в пикселях"}, "height": {"label": "Высота", "description": "Высота изображения в пикселях"}, "guidance_scale": {"label": "Масштаб руководства", "description": "Соответствие запросу. Более высокие значения = более буквальная интерпретация запроса."}}}, "qwenImageConfig": {"modelName": "<PERSON><PERSON>", "description": "Модель генерации изображений с продвинутыми возможностями рендеринга текста и точного редактирования изображений", "parameters": {"enhance_prompt": {"label": "Улучшить запрос", "description": "Улучшить запрос с помощью позитивной магии"}, "negative_prompt": {"label": "Негативный запрос", "description": "Негативный запрос для генерируемого изображения", "placeholder": "Низкое качество, размытый, искаженный..."}, "image_size": {"label": "Размер изображения", "description": "Размер изображения для генерируемого изображения", "options": {"square_hd": "Квадр<PERSON><PERSON> HD", "square": "Ква<PERSON><PERSON><PERSON><PERSON>", "portrait_4_3": "Портрет 4:3", "portrait_16_9": "Портрет 16:9", "landscape_4_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 4:3", "landscape_16_9": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16:9"}}, "go_fast": {"label": "Быстрый режим", "description": "Ускоренные предсказания с дополнительными оптимизациями"}, "num_inference_steps": {"label": "Шаги шумоподавления", "description": "Количество шагов шумоподавления. Рекомендуемый диапазон 28-50, меньшее количество шагов даёт более низкое качество, но быстрее"}, "guidance": {"label": "Руководство", "description": "Руководство для сгенерированного изображения. Меньшие значения могут давать более реалистичные изображения. Хорошие значения: 2, 2.5, 3, 3.5"}, "output_format": {"label": "Формат вывода", "description": "Формат выходных изображений"}, "output_quality": {"label": "Качество вывода", "description": "Качество при сохранении выходных изображений, от 0 до 100. 100 - лучшее качество, 0 - самое низкое. Не относится к PNG выходам"}, "disable_safety_checker": {"label": "Отключить проверку безопасности", "description": "Отключить проверку безопасности для сгенерированных изображений"}, "lora_weights": {"label": "LoRA веса", "description": "Загрузить LoRA веса. Поддерживает модели Replicate в формате <owner>/<username>, URL HuggingFace в формате huggingface.co/<owner>/<model-name>, URL CivitAI или произвольные .safetensors URL", "placeholder": "например, 'fofr/flux-pixar-cars'"}, "lora_scale": {"label": "Масштаб LoRA", "description": "Определяет, насколько сильно должна применяться основная LoRA. Разумные результаты между 0 и 1 для базового инференса"}}, "groups": {"lora": {"label": "LoRA Настройки", "description": "Настройки для загрузки и применения LoRA адаптеров"}}}, "common": {"parameters": {"aspect_ratio": {"label": "Соотношение сторон", "description": "Соотношение сторон сгенерированного изображения", "options": {"1:1": "1:1 (К<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "16:9": "16:9 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "21:9": "21:9 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "3:2": "3:2 (Фото)", "2:3": "2:3 (Фотопортрет)", "4:5": "4:5 (Портр<PERSON><PERSON>)", "5:4": "5:4 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "3:4": "3:4 (Портр<PERSON>т)", "4:3": "4:3 (Стан<PERSON><PERSON><PERSON><PERSON>ны<PERSON>)", "9:16": "9:16 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>й портрет)", "9:21": "9:21 (Вы<PERSON><PERSON><PERSON><PERSON> портрет)", "match_input_image": "Соответствовать входному изображению", "2:1": "2:1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "1:2": "1:2 (Выс<PERSON><PERSON><PERSON>)", "custom": "Пользовательский (указать ширину/высоту)"}}, "output_format": {"label": "Формат вывода", "description": "Формат выходных изображений", "options": {"webp": "WebP (Рекомендуется)", "jpg": "JPEG", "png": "PNG"}}, "seed": {"title": "Сид", "description": "Случайное начальное значение. Устанавливается для воспроизводимой генерации"}}}}}, "contentGallery": {"title": "Недавние Творения", "sortBy": "Сортировать по:", "sortOptions": {"mostRecent": "Самые Недавние", "mostLiked": "Самые Популярные", "mostViewed": "Самые Просматриваемые"}, "filterBy": "Фильтр:", "filterOptions": {"all": "Все", "videos": "Видео", "images": "Изображения"}, "views": "просмотров"}, "generationHistory": {"title": "История Генераций", "description": "Просмотрите все ваши предыдущие генерации видео и изображений", "showing": "Показано", "to": "до", "of": "из", "generations": "генера<PERSON>ий", "show": "Показать", "perPage": "на странице", "filters": {"all": "Все", "images": "Изображения", "videos": "Видео"}, "noGenerations": "Генерации не найдены", "loadMore": "Загрузить Еще", "model": "Модель", "error": "Ошибка", "pagination": {"previous": "Предыдущая", "next": "Следующая"}, "status": {"pending": "Ожидание", "processing": "Обработка", "completed": "Завершено", "failed": "Не удалось"}, "type": {"video": "Видео", "image": "Изображение"}, "copyPrompt": "Копировать промпт", "copySuccess": "Промпт скопирован!", "copySuccessDescription": "Промпт был скопирован в буфер обмена.", "copyError": "Ошибка копирования", "copyErrorDescription": "Не удалось скопировать промпт. Попробуйте еще раз.", "noGenerationsYet": "Пока нет генераций", "noGenerationsDescription": "Начните создавать видео и изображения, чтобы увидеть историю здесь."}, "notFound": {"title": "404", "message": "Упс! Страница не найдена", "returnHome": "Вернуться Домой"}, "privacyPolicy": {"title": "Политика Конфиденциальности", "lastUpdated": "Редакция от 1 Июля 2025 г.", "content": "Настоящая Политика конфиденциальности (далее — «Политика») описывает, как ИП Ступаков Ю.В. (далее — «Компания», «мы», «нас») собирает, использует и защищает ваши персональные данные при использовании вами нашего сервиса «Холст.ИИ» (далее — «Сервис»), доступного на сайте holstai.ru.\n \nИспользуя наш Сервис, вы соглашаетесь со сбором и использованием информации в соответствии с настоящей Политикой.\n \n1. Какие данные мы собираем\n \nМы собираем несколько типов данных для различных целей, чтобы предоставлять и улучшать наш Сервис.\n \n1.1. Данные, которые вы предоставляете нам напрямую:\n \nРегистрационные данные:  При создании учетной записи мы просим вас предоставить адрес электронной почты и создать пароль. Вы также можете указать имя пользователя (псевдоним).\nПлатежная информация:  При оплате подписки или других услуг мы не храним полные данные вашей банковской карты. Эти данные обрабатываются нашими сертифицированными платежными партнерами (например, Stripe, YooKassa). Мы получаем только информацию о статусе платежа, тип и последние четыре цифры карты.\nДанные, генерируемые пользователем:\n​Текстовые запросы (промпты):  Мы сохраняем текстовые запросы, которые вы вводите для генерации изображений и видео.\n​Сгенерированный контент:  Мы храним изображения и видео, созданные вами в Сервисе, чтобы вы могли получить к ним доступ в вашей личной галерее.\n​Данные для связи:  Когда вы обращаетесь в нашу службу поддержки, мы сохраняем историю переписки, чтобы помочь вам решить ваш вопрос.\n \n1.2. Данные, которые мы собираем автоматически:\n \nТехнические данные:  IP-адрес, тип и версия браузера, операционная система, тип устройства, часовой пояс и информация о вашем интернет-соединении.\nДанные об использовании:  Информация о том, как вы взаимодействуете с Сервисом: какие страницы посещаете, какие функции используете, время и дата ваших визитов, время, проведенное на страницах.\nФайлы Cookie:  Мы используем файлы cookie и аналогичные технологии для отслеживания активности в нашем Сервисе и хранения определенной информации. Подробнее — в разделе «Использование файлов cookie».\n \n2. Как мы используем ваши данные\n \nМы используем собранные данные для следующих целей:\n \nДля предоставления и поддержки Сервиса:  Для создания и управления вашей учетной записью, обработки платежей и предоставления вам доступа ко всем функциям.\nДля выполнения ваших запросов:  Для обработки ваших текстовых промптов и генерации изображений и видео.\nДля улучшения Сервиса:  Мы можем использовать  анонимизированные и агрегированные  данные (включая текстовые запросы и сгенерированный контент) для обучения и улучшения наших AI-моделей. Это помогает нам делать генерацию более точной и качественной.  Мы не используем ваши персональные данные или уникальный контент для обучения без вашего явного согласия.\nДля анализа и статистики:  Для понимания того, как используется наш Сервис, чтобы мы могли улучшать пользовательский опыт и разрабатывать новые функции.\nДля обеспечения безопасности:  Для мониторинга и предотвращения мошенничества, несанкционированного доступа и других незаконных действий.\nДля коммуникации с вами:  Для отправки вам сервисных уведомлений, ответов на ваши запросы, а также маркетинговых сообщений (если вы дали на это согласие).\n \n3. Передача данных третьим лицам\n \nМы не продаем ваши персональные данные. Мы можем передавать их третьим лицам только в следующих случаях:\n \nПоставщикам услуг:  Мы привлекаем сторонние компании для оказания услуг от нашего имени (например, хостинг данных, обработка платежей, аналитика, облачные вычисления для работы AI). Эти компании получают доступ к вашим данным только для выполнения этих задач и обязаны не разглашать и не использовать их в других целях.\nПо требованию закона:  Мы можем раскрыть ваши данные, если этого требует закон или добросовестное предположение, что такие действия необходимы для соблюдения юридических обязательств или защиты прав и безопасности Компании или пользователей.\n \n4. Права пользователя\n \nВы имеете право:\n \nНа доступ:  Запросить информацию о том, какие ваши персональные данные у нас хранятся.\nНа исправление:  Потребовать исправления неточных или неполных данных.\nНа удаление:  Запросить удаление вашей учетной записи и всех связанных с ней данных («право на забвение»).\nНа ограничение обработки:  Потребовать ограничить обработку ваших данных в определенных случаях.\nНа возражение:  Возразить против обработки ваших данных в целях прямого маркетинга.\n \nДля реализации этих прав, пожалуйста, свяжитесь с нами по адресу <EMAIL>.\n \n5. Защита данных\n \nМы принимаем все разумные технические и организационные меры для защиты ваших данных от несанкционированного доступа, изменения, раскрытия или уничтожения. К ним относятся шифрование данных (SSL/TLS), хэширование паролей, ограничение доступа к данным для наших сотрудников и регулярные аудиты безопасности. Однако ни один метод передачи данных через Интернет не является на 100% безопасным.\n \n6. Использование файлов cookie\n \nФайлы cookie — это небольшие файлы, хранящиеся на вашем устройстве. Мы используем их для аутентификации, сохранения ваших настроек, аналитики и маркетинга. Вы можете управлять файлами cookie через настройки вашего браузера. Обратите внимание, что отключение cookie может повлиять на работоспособность некоторых функций Сервиса.\n \n7. Конфиденциальность детей\n \nНаш Сервис не предназначен для лиц младше 18 лет. Мы сознательно не собираем персональные данные детей. Если вы считаете, что мы получили данные от ребенка, пожалуйста, свяжитесь с нами, и мы примем меры для удаления этой информации.\n \n8. Изменения в Политике конфиденциальности\n \nМы можем время от времени обновлять нашу Политику. Мы уведомим вас о любых изменениях, опубликовав новую версию на этой странице и, в случае существенных изменений, отправив уведомление по электронной почте. Рекомендуем вам периодически просматривать эту Политику.\n \n9. Контактная информация\n \nЕсли у вас есть вопросы по поводу этой Политики конфиденциальности, пожалуйста, свяжитесь с нами:\n \nИП Ступаков Ю.В.\nEmail: <EMAIL>"}, "termsOfUse": {"title": "Условия Использования", "lastUpdated": "Редакция от 1 Июля 2025 г.", "content": "Добро пожаловать в «Холст.ИИ»!\n \nНастоящее Пользовательское соглашение (далее — «Условия») представляет собой юридически обязательный договор между вами (далее — «Пользователь», «вы») и ИП Ступаков Ю.В. (далее — «Компания», «мы»), регулирующий использование вами веб-сайта holstai.ru и всех связанных с ним сервисов, инструментов и функций для генерации контента с помощью искусственного интеллекта (совместно именуемые «Сервис»).\n \nИспользуя наш Сервис, вы подтверждаете, что прочитали, поняли и безоговорочно согласны соблюдать настоящие Условия. Если вы не согласны с какими-либо из этих условий, вы не должны использовать Сервис.\n \n1. Описание Сервиса  \n \n«Холст.ИИ» — это технологическая платформа, которая позволяет пользователям создавать уникальные цифровые изображения и видео (далее — «Сгенерированный Контент») на основе текстовых запросов (далее — «Промпты») с использованием алгоритмов искусственного интеллекта.\n \n2. Регистрация и Учетная запись  \n \n2.1. Для доступа к полному функционалу Сервиса вам необходимо создать учетную запись. Вы должны предоставить точную и актуальную информацию, включая действующий адрес электронной почты.\n2.2. Вы несете полную ответственность за сохранение конфиденциальности вашего пароля и за все действия, которые происходят под вашей учетной записью. Немедленно уведомите нас о любом несанкционированном использовании вашего аккаунта.\n2.3. Использовать Сервис могут только лица, достигшие 18 лет. Регистрируясь, вы подтверждаете, что достигли совершеннолетия.\n \n3. Права на интеллектуальную собственность  \n \n3.1.   Наш Сервис.   Все права на Сервис, включая веб-сайт, дизайн, программный код, лежащие в его основе AI-модели, товарные знаки и другие объекты интеллектуальной собственности, принадлежат Компании ИП Ступаков Ю.В.\n3.2.   Ваши Промпты.   Вы сохраняете все права на созданные вами текстовые Промпты.\n3.3.   Сгенерированный Контент.     Вы являетесь владельцем Сгенерированного Контента, созданного вами с помощью Сервиса.   Мы передаем вам все права на созданные вами изображения и видео, и вы можете использовать их в личных и коммерческих целях при условии, что ваше использование не нарушает настоящие Условия, в частности, раздел 4.\n3.4.   Лицензия для нас.   Для обеспечения работы Сервиса (например, для отображения вашего контента в вашей галерее) вы предоставляете Компании всемирную, неисключительную, безвозмездную лицензию на использование, воспроизведение, хранение, изменение (для технических целей, например, создания превью) и отображение ваших Промптов и Сгенерированного Контента. Эта лицензия действует исключительно для целей предоставления, поддержки и улучшения Сервиса. Мы можем использовать анонимизированные данные для улучшения наших AI-моделей.\n \n4. Правила использования и запрещенные действия  \n \nВы соглашаетесь не использовать Сервис для создания, загрузки или распространения контента, который:\n \nЯвляется незаконным, мошенническим, дискредитирующим, клеветническим, непристойным или порнографическим.\nСодержит изображения сексуального насилия, эксплуатации детей или нарушает их права.\nРазжигает ненависть, дискриминацию, насилие в отношении отдельных лиц или групп по признаку расы, религии, пола, сексуальной ориентации, инвалидности или этнического происхождения.\nНарушает права интеллектуальной собственности третьих лиц, включая авторские права, товарные знаки и патенты.\nПредставляет собой дезинформацию, способную нанести вред обществу или отдельным лицам.\nНарушает права на частную жизнь, используя личные данные третьих лиц без их согласия.\nПредназначен для создания вредоносного ПО, фишинга или других видов кибератак.\n \nВы также обязуетесь не производить обратное проектирование, декомпиляцию или попытки извлечь исходный код нашего Сервиса, и не использовать автоматизированные системы (скрипты, боты) для чрезмерной нагрузки на нашу инфраструктуру.\n \nНарушение этих правил может привести к немедленному прекращению вашего доступа к Сервису и удалению вашего контента и учетной записи.\n \n5. Оплата, подписка и возврат средств  \n \n5.1. Доступ к некоторым функциям Сервиса может требовать оплаты в рамках тарифного плана или подписки. Все цены указаны на соответствующей странице сайта.\n5.2. Подписки автоматически продлеваются в конце каждого расчетного периода (например, ежемесячно или ежегодно), если вы не отмените их до даты следующего списания.\n5.3. Вы можете отменить подписку в любой момент в настройках вашего аккаунта. Доступ к платным функциям сохранится до конца уже оплаченного периода.\n5.4. Все платежи являются окончательными и не подлежат возврату, за исключением случаев, прямо предусмотренных законодательством или если Сервис был недоступен по нашей вине в течение значительного времени.\n \n6. Отказ от гарантий и ограничение ответственности  \n \n6.1. Сервис предоставляется на условиях «КАК ЕСТЬ» и «КАК ДОСТУПНО». Мы не даем никаких гарантий, что Сервис будет работать без ошибок, бесперебойно, или что Сгенерированный Контент будет точным, надежным или соответствовать вашим ожиданиям.\n6.2. Искусственный интеллект может генерировать неточный, ошибочный или даже оскорбительный контент. Вы используете Сгенерированный Контент на свой страх и риск и несете ответственность за его проверку перед использованием.\n6.3. Ни при каких обстоятельствах ИП Ступаков Ю.В., сотрудники или партнеры не несут ответственности за любые косвенные, случайные, специальные или штрафные убытки, включая упущенную выгоду, потерю данных или деловой репутации, возникшие в результате использования или невозможности использования Сервиса.\n6.4. Наша совокупная ответственность по любым претензиям, связанным с использованием Сервиса, не может превышать сумму, уплаченную вами за использование Сервиса за последние 6 (шесть) месяцев.\n \n7. Прекращение действия  \n \nМы можем приостановить или прекратить ваш доступ к Сервису немедленно, без предварительного уведомления, если вы нарушите настоящие Условия. Вы можете прекратить использование Сервиса в любое время, удалив свою учетную запись.\n \n8. Применимое право и разрешение споров  \n \nНастоящие Условия регулируются и толкуются в соответствии с законодательством Российской Федерации. Все споры и разногласия, возникающие из настоящих Условий, подлежат разрешению путем переговоров, а в случае недостижения согласия — в суде по месту нахождения Компании.\n \n9. Изменения в Условиях  \n \nМы оставляем за собой право изменять или заменять настоящие Условия в любое время. В случае внесения существенных изменений мы уведомим вас по электронной почте или путем размещения уведомления на нашем сайте. Продолжая использовать Сервис после вступления изменений в силу, вы соглашаетесь с новой редакцией Условий.\n \n10. Контактная информация  \n \nЕсли у вас есть вопросы по этим Условиям, свяжитесь с нами:\nИП Ступаков Ю.В.  \nEmail: <EMAIL>"}, "seo": {"main": {"title": "Холст.ИИ - Создавайте картинки и видео без VPN и ограничений", "description": "Создавайте изображения и видео без VPN и ограничений. Наши ИИ модели — ваш новый инструмент для безграничного творчества.", "keywords": "ИИ генерация, создание изображений, генерация видео, искусственный интеллект, нейросети, творчество, дизайн, контент"}, "smm": {"title": "Создание контента для SMM с помощью ИИ - Холст.ИИ", "description": "Упростите и ускорьте процесс создания контента для постов в социальных сетях. Создавайте яркий визуал, вдохновляющий и привлекающий внимание.", "keywords": "SMM контент, социальные сети, визуал для соцсетей, контент маркетинг, ИИ для SMM, создание постов"}, "marketplace": {"title": "Создание обложек для маркетплейсов с ИИ - Холст.ИИ", "description": "Загрузите фото или просто опишите товар — ИИ подготовит готовые изображения и видео, соответствующие стандартам OZON, Wildberries, Яндекс Маркет и других площадок.", "keywords": "обложки маркетплейс,обложки вайлдберрис,Wildberries, OZON, ЯндексМаркет,  Amazon, товарные фото, 3D рендеры, баннеры для акций"}, "inspiration": {"title": "Поиск вдохновения с помощью ИИ - Холст.ИИ", "description": "Больше не нужно тратить часы на Pinterest и бесконечный скролл соцсетей. Опишите настроение, тему или идею — и искусственный интеллект подберёт образы, цветовые палитры и композиции.", "keywords": "вдохновение, креативные идеи, цветовые палитры, визуальные референсы, дизайн идеи, творческий процесс"}, "livingPhoto": {"title": "Оживление персонажей с помощью ИИ - Холст.ИИ", "description": "Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд.", "keywords": "оживление фото, оживление персонажей, фото в видео, анимация изображений, живые фото, динамичные визуалы, анимация для соцсетей"}, "influencer": {"title": "Создание виртуального инфлюэнсера с ИИ - Холст.ИИ", "description": "Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты.", "keywords": "виртуальный инфлюэн<PERSON>е<PERSON>, инстаграмм influencer, ии блогер, цифровой герой, создание персонажа, Instagram, TikTok, YouTube, VK, RuTube"}}}
{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "search": "Search", "filter": "Filter", "sort": "Sort", "all": "All", "none": "None", "yes": "Yes", "no": "No"}, "header": {"brand": "Holst AI", "navigation": {"features": "Features", "gallery": "Gallery", "pricing": "Pricing", "faq": "FAQ"}, "getStarted": "Get Started"}, "hero": {"title": "Create", "titleHighlight": "<PERSON><PERSON><PERSON>", "titleSuffix": "AI Content", "subtitle": "Transform your ideas into incredible images and videos using our most intelligent AI models. Professional quality results in seconds.", "startCreating": "Start Creating", "viewExamples": "View Examples", "supportedModels": "Supported AI Models", "noCardRequired": "No card required", "noVPNRequired": "No VPN", "noSubscription": "No subscription", "stats": {"value1": "1M+", "label1": "Images Generated", "value2": "50K+", "label2": "Videos Created", "value3": "99.9%", "label3": "Uptime"}}, "landings": {"smm": {"pageName": "Create content for SMM", "title": "Create", "titleHighlight": "Amazing content for social media", "subtitle": "Simplify and speed up the process of creating content for posts on social networks. Create eye-catching visuals, inspiring and attention-grabbing."}, "marketplace": {"pageName": "Create covers for marketplaces", "title": "Create", "titleHighlight": "Captivating visuals for marketplaces in minutes", "subtitle": "Upload a photo or simply describe a product - AI will prepare ready-made images and videos that meet the standards of OZON, Wildberries, Amazon, and other platforms. Selling covers, 3D renders, banners for promotions - without a photo studio and lengthy approvals."}, "inspiration": {"pageName": "Find inspiration", "title": "Inspiration", "titleHighlight": "at your fingertips", "subtitle": "No more hours spent on Pinterest or endless scrolling through social networks. Describe a mood, theme or idea - and AI will select visuals, color palettes and compositions that will help kickstart your creative process and find a new direction for your projects."}, "livingPhoto": {"pageName": "Bring your photos to life", "title": "Bring your photos to life", "titleHighlight": "With AI", "subtitle": "Transform ordinary images into dynamic and lively visuals. AI adds movement, emotions, and atmosphere, creating animations that grab attention. Perfect for social media, presentations, and advertising - bring your photos to life without complicated software or studios."}, "influence": {"pageName": "Create a virtual influencer", "title": "Create", "titleHighlight": "A virtual influencer in minutes", "subtitle": "Come up with the perfect digital hero for social media. You set the appearance, style and character - AI creates a unique influencer, ready to manage accounts, publish posts and interact with the audience. Perfect for Instagram, TikTok, YouTube or other platforms."}}, "features": {"title": "Powerful Features for", "secondTitle": "Out advantages", "titleHighlight": "Creative Minds", "subtitle": "Everything you need to bring your creative visions to life with cutting-edge AI technology", "items": {"aiPowered": {"title": "AI-Powered Generation", "description": "Advanced neural networks create stunning visuals from simple text prompts with unprecedented quality and creativity."}, "lightningFast": {"title": "Lightning Fast", "description": "Generate high-quality images and videos in seconds, not hours. Our optimized infrastructure ensures rapid processing."}, "artisticControl": {"title": "Artistic Control", "description": "Fine-tune styles, adjust parameters, and guide the AI to match your creative vision with precision controls."}, "multipleFormats": {"title": "Multiple Formats", "description": "Export in various formats including 4K images, HD videos, and formats optimized for social media platforms."}, "commercialLicense": {"title": "Commercial License", "description": "Full commercial rights to generated content. Use your creations for business, marketing, and commercial projects."}, "advancedModels": {"title": "Advanced Models", "description": "Access to the latest AI models including diffusion models, transformers, and our proprietary generation algorithms."}}}, "gallery": {"title": "Incredible", "titleHighlight": "AI Creations", "subtitle": "Explore the limitless possibilities of AI-generated content. From photorealistic images to dynamic videos.", "tabs": {"images": "Images", "videos": "Videos"}, "imageAlt": "AI Generated Image", "videoAlt": "AI Generated Video", "sampleImages": [{"prompt": "A cinematic, photorealistic medium shot capturing the nostalgic warmth of a mid-2000s indie film. The focus is a young woman with a sleek, straight bob haircut in cool platinum white with freckled skin, looking directly and intently into the camera lens with a knowing smirk, her head is looking up slightly. She wears an oversized band t-shirt that says \"Seedream 3.0 on Replicate\" in huge stylized text over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight creating lens flare and illuminating dust motes in the air. The background shows a blurred outdoor urban setting with graffiti-covered walls (the graffiti says \"seedream\" in stylized graffiti lettering), rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel", "model": "Seedream-3"}, {"prompt": "The photo: Create a cinematic, photorealistic medium shot capturing the nostalgic warmth of a late 90s indie film. The focus is a young woman with brightly dyed pink-gold hair and freckled skin, looking directly and intently into the camera lens with a hopeful yet slightly uncertain smile, she is slightly off-center. She wears an oversized, vintage band t-shirt that says \"Replicate\" (slightly worn) over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight streaming through a slightly dusty window, creating lens flare and illuminating dust motes in the air. The background shows a blurred, cluttered bedroom with posters on the wall and fairy lights, rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel", "model": "Imagen 4 Ultra"}, {"prompt": "Using this <PERSON> style, a panda astronaut riding a unicorn", "model": "Flux Kontext Pro"}, {"prompt": "black forest gateau cake spelling out the words \"FLUX DEV\", tasty, food photography, dynamic shot", "model": "Flux Dev"}], "sampleVideos": [{"prompt": "Bearded ancient philosopher in classical robes teaching wisdom to students in a marble garden setting, speaking with modern youthful language and expressions. The teacher gestures while sharing philosophical concepts using contemporary slang. Students in period clothing listen attentively. Warm natural lighting, classical architecture background, blending timeless wisdom with current speech pattern", "model": "Google Veo3"}, {"prompt": "The sun rises slowly between tall buildings. [Ground-level follow shot] Bicycle tires roll over a dew-covered street at dawn. The cyclist passes through dappled light under a bridge as the entire city gradually wakes up.", "model": "Seedance Pro"}, {"prompt": "A woman walks in the park", "model": "Seedance Lite"}, {"prompt": "A cat walks on the grass, realistic style", "model": "Huyuan Video"}]}, "faq": {"title": "Frequently Asked", "titleHighlight": "Questions", "subtitle": "Everything you need to know about Holst AI and our AI-powered content generation platform.", "items": {"whatIsAi": {"question": "What is AI-powered content generation?", "answer": "AI-powered content generation uses advanced machine learning models to create images, videos, and other visual content from text descriptions. Our AI understands your prompts and generates high-quality, unique content in seconds."}, "howLong": {"question": "How long does it take to generate content?", "answer": "Most images are generated within 5-15 seconds, while videos typically take 30-60 seconds. Processing times may vary based on complexity and current system load."}, "fileFormats": {"question": "What file formats are supported?", "answer": "We support multiple formats including PNG, JPEG, WebP for images, and MP4, WebM for videos. You can also export in various resolutions up to 4K quality."}, "commercial": {"question": "Can I use the generated content commercially?", "answer": "Yes! All paid plans include full commercial licensing. You own the rights to all content you generate and can use it for any commercial purpose without attribution."}, "billing": {"question": "How does the billing work?", "answer": "We offer monthly and annual subscription plans. You're billed at the beginning of each billing cycle, and you can cancel or upgrade your plan at any time."}, "dataPrivacy": {"question": "What happens to my data and generated content?", "answer": "Your data is securely stored and encrypted. Generated content belongs to you, and we don't use it to train our models. You can delete your account and data at any time."}, "cancellation": {"question": "Can I cancel my subscription anytime?", "answer": "Absolutely! You can cancel your subscription at any time from your account settings. You'll continue to have access until the end of your current billing period."}, "refunds": {"question": "Do you offer refunds?", "answer": "We offer a 14-day money-back guarantee for all new subscriptions. If you're not satisfied within the first 14 days, contact our support team for a full refund."}}}, "footer": {"brand": "Holst AI", "description": "Transform your creative vision into reality with our cutting-edge AI technology. Generate stunning visuals and videos in seconds.", "sections": {"product": {"title": "Product", "features": "Features", "gallery": "Gallery", "pricing": "Pricing", "api": "API"}, "company": {"title": "Company", "aboutUs": "About Us", "blog": "Blog", "careers": "Careers", "press": "Press"}, "support": {"title": "Support", "helpCenter": "Help Center", "faq": "FAQ", "contact": "Contact", "status": "Status"}, "legal": {"title": "Legal", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "gdpr": "GDPR"}}, "copyright": "© 2025 HolstAI. All rights reserved."}, "login": {"title": "Welcome back", "subtitle": "Sign in to your HolstAI account", "continueWithGoogle": "Continue with Google", "continueWithYandex": "Continue with Yandex", "continueWithVK": "Continue with VK"}, "sidebar": {"brand": "HolstAI", "navigation": {"create": "Create", "history": "History", "pricing": "Pricing", "support": "Support"}, "user": {"defaultName": "User", "credits": "credits", "profileAlt": "User Profile"}}, "studio": {"title": "Create with AI", "description": "Turn your imagination into reality"}, "profile": {"title": "Profile", "description": "Manage your account and view your creations", "sections": {"profileInformation": "Profile Information", "credits": "Credits", "referral": "Referral Program"}, "fields": {"fullName": "Full Name", "email": "Email", "username": "Username"}, "buttons": {"saveChanges": "Save Changes", "logout": "Logout", "topup": "Top Up Credits", "paymentComingSoon": "Payment coming soon..."}, "stats": {"creditsRemaining": "Credits remaining"}, "congratulations": {"title": "Payment Successful! 🎉", "message": "Your account has been topped up with {{amount}} credits!", "subtitle": "Thank you for your purchase. You can now create amazing content with AI!", "continueButton": "Continue Creating"}, "referral": {"title": "Referral Program", "description": "Invite friends and earn credits together!", "offer": "Get 50% of the credits your friend pays for and 25 credits for each friend signed up", "linkLabel": "Your referral link", "copyButton": "Copy Link", "copySuccess": "Referral link copied!", "copySuccessDescription": "Share this link with friends to start earning credits.", "copyError": "Co<PERSON> failed", "copyErrorDescription": "Failed to copy the referral link. Please try again."}, "recentGenerations": {"title": "Recent Generations", "viewAll": "View All", "type": "Type", "title_field": "Title", "date": "Date", "status": "Status", "completed": "Completed", "processing": "Processing"}}, "pricing": {"title": "Simple Token", "titleHighlight": "Pricing", "subtitle": "Pay only for what you use. No subscriptions, no hidden fees. Buy tokens and use them across all our AI models.", "popular": "Most Popular", "tokenPackages": {"title": "Token Packages", "subtitle": "Choose the amount of tokens that fits your needs", "packages": {"small": {"name": "100 Tokens", "price": "$2.99", "pricePerToken": "$0.0299 per token", "description": "Perfect for trying out our AI models", "buttonText": "Buy 100 Tokens"}, "medium": {"name": "500 Tokens", "price": "$12.99", "pricePerToken": "$0.0260 per token", "description": "Great for regular creative projects", "savings": "13% off", "buttonText": "Buy 500 Tokens"}, "large": {"name": "1000 Tokens", "price": "$19.99", "pricePerToken": "$0.0200 per token", "description": "Best value for power users", "savings": "33% off", "buttonText": "Buy 1000 Tokens"}}}, "modelCosts": {"title": "Per-Model Token", "titleHighlight": "Costs", "subtitle": "Different AI models have different computational requirements. Here's how many tokens each model costs per generation.", "imageModels": {"title": "Image Generation Models", "models": {"fluxDev": {"name": "FLUX Dev", "cost": "3 tokens", "description": "Fast, high-quality image generation", "pricing": "3 tokens × number of outputs"}, "seedream3": {"name": "Seedream 3", "cost": "3 tokens", "description": "Native 2K resolution support", "pricing": "3 tokens per image"}, "imagen4Ultra": {"name": "Imagen 4 Ultra", "cost": "6 tokens", "description": "Ultra high-resolution images", "pricing": "6 tokens per image"}, "fluxKontextPro": {"name": "FLUX Kontext Pro", "cost": "4 tokens", "description": "Professional image editing", "pricing": "4 tokens per image"}}}, "videoModels": {"title": "Video Generation Models", "models": {"seedanceLite": {"name": "Seedance Lite", "description": "Fast video generation up to 10 seconds", "variants": {"480p": "2 tokens × duration (seconds)", "1080p": "4 tokens × duration (seconds)"}}, "seedancePro": {"name": "Seedance Pro", "description": "Professional video generation up to 10 seconds", "variants": {"480p": "3 tokens × duration (seconds)", "1080p": "15 tokens × duration (seconds)"}}, "hunyuan": {"name": "Hunyuan Video", "cost": "250 tokens", "description": "High-quality video generation", "pricing": "250 tokens per video"}, "veo2": {"name": "Veo 2", "cost": "50 tokens", "description": "Advanced video capabilities", "pricing": "50 tokens × duration (seconds)"}, "veo3": {"name": "Veo 3", "cost": "75 tokens", "description": "Latest video generation technology", "pricing": "75 tokens × 8 seconds = 600 tokens per video"}, "veo3-fast": {"name": "Veo 3 Fast", "cost": "40 tokens", "description": "Latest video generation technology - faster and cheaper", "pricing": "40 tokens × 8 секунд = 320 tokens per video"}}}}, "examples": {"title": "Pricing", "titleHighlight": "Examples", "subtitle": "See how much common generation tasks cost with our token system.", "categories": {"singleImage": {"title": "Single Image", "items": {"fluxDev": "FLUX Dev (1 image): 3 tokens", "seedream3": "Seedream 3 (1 image): 3 tokens", "imagen4Ultra": "Imagen 4 Ultra (1 image): 6 tokens"}}, "batchGeneration": {"title": "Batch Generation", "items": {"fluxDev": "FLUX Dev (4 images): 12 tokens", "seedream3": "Seedream 3 (4 images): 12 tokens", "imagen4Ultra": "Imagen 4 Ultra (4 images): 24 tokens"}}, "shortVideos": {"title": "Short Videos", "items": {"seedanceLite480": "Seedance Lite 480p (5s): 10 tokens", "seedancePro480": "Seedance Pro 480p (5s): 15 tokens", "veo2": "Veo 2 (5s): 250 tokens"}}, "hdVideos": {"title": "HD Videos", "items": {"seedanceLite1080": "Seedance Lite 1080p (5s): 20 tokens", "seedancePro1080": "Seedance Pro 1080p (5s): 75 tokens", "hunyuan": "Hunyuan Video: 250 tokens"}}, "premiumVideos": {"title": "Premium Videos", "items": {"veo3": "Veo 3 (8s fixed): 600 tokens", "veo2Long": "Veo 2 (10s): 500 tokens", "seedanceProLong": "Seedance Pro 1080p (10s): 150 tokens"}}, "costComparison": {"title": "Cost Comparison", "subtitle": "With 500 tokens ($12.99):", "items": {"fluxDevImages": "~166 FLUX Dev images: $0.078 each", "seedanceLiteVideos": "~25 Seedance Lite videos (5s): $0.52 each", "hunyuanVideos": "~2 Hunyuan videos: $6.50 each"}}}}, "faq": {"title": "Frequently Asked Questions", "items": {"tokensExpire": {"question": "Do tokens expire?", "answer": "No, your tokens never expire. Buy them once and use them whenever you want to create content."}, "videoCosts": {"question": "How are video costs calculated?", "answer": "Video costs depend on the model, resolution, and duration. For example, Seedance models charge per second, while Veo 3 has a fixed 8-second duration."}, "generationFails": {"question": "What happens if generation fails?", "answer": "If a generation fails due to our system error, your tokens will be automatically refunded to your account."}, "modelPrices": {"question": "Why do models have different prices?", "answer": "Different AI models require varying amounts of computational resources. More advanced models like Veo 3 and Hunyuan cost more due to their superior quality and capabilities."}, "bulkDiscounts": {"question": "Can I get bulk discounts?", "answer": "Yes! Larger token packages offer better per-token rates. The 1000-token package saves you 33% compared to the 100-token package."}, "trackUsage": {"question": "How do I track my token usage?", "answer": "Your current token balance is displayed in your profile, and each generation shows the exact token cost before you confirm."}}}, "cta": {"subtitle": "Ready to start creating? Sign up and get started with our AI models.", "buttonText": "Get Started Now"}}, "videoGenerator": {"tabs": {"video": "Video", "image": "Image"}, "selectModel": "Select Model", "promptLabel": "Describe what you want to create", "promptPlaceholder": "A serene mountain landscape at sunrise with mist rolling through the valleys, cinematic lighting, ultra-detailed...", "translatePrompt": {"label": "Translate prompt to English"}, "models": {"hunyuan": {"name": "Hunyuan Video", "description": "A state-of-the-art text-to-video generation model capable of creating high-quality videos with realistic motion from text descriptions"}, "veo2": {"name": "Veo 2", "description": "State of the art video generation model. Veo 2 can faithfully follow simple and complex instructions, and convincingly simulates real-world physics as well as a wide range of visual styles."}, "veo3": {"name": "Veo 3", "description": "Google's flagship Veo 3 text to video model, with audio generation capabilities"}, "veo3-fast": {"name": "Veo 3 Fast", "description": "A faster and cheaper version of Google's Veo 3 video model, with audio generation capabilities"}, "seedance-pro": {"name": "Seedance Pro", "description": "A pro version of Seedance that offers text-to-video and image-to-video support for 5s or 10s videos, at 480p and 1080p resolution"}, "seedance-lite": {"name": "Seedance Lite", "description": "A video generation model that offers text-to-video and image-to-video support for 5s or 10s videos, at 480p and 720p resolution"}, "flux-dev": {"name": "FLUX Dev", "description": "A 12 billion parameter rectified flow transformer capable of generating images from text descriptions"}, "flux-kontext-pro": {"name": "FLUX Kontext Pro", "description": "A state-of-the-art text-based image editing model that delivers high-quality outputs with excellent prompt following and consistent results for transforming images through natural language"}, "imagen-4-ultra": {"name": "Imagen 4 Ultra", "description": "Use this ultra version of Imagen 4 when quality matters more than speed and cost"}, "seedream-3": {"name": "Seedream 3", "description": "A text-to-image model with support for native high-resolution (2K) image generation"}}, "parameters": {"labels": {"width": "<PERSON><PERSON><PERSON>", "height": "Height", "video_length": "Video Length", "infer_steps": "Inference Steps", "embedded_guidance_scale": "Guidance Scale", "fps": "FPS", "seed": "Seed", "image": "Input Image (Optional)", "aspect_ratio": "Aspect Ratio", "duration": "Duration", "enhance_prompt": "Enhance Prompt", "negative_prompt": "Negative Prompt", "resolution": "Resolution", "camera_fixed": "Fixed Camera", "num_outputs": "Number of Images", "num_inference_steps": "Inference Steps", "guidance": "Guidance", "go_fast": "Go Fast", "megapixels": "Megapixels", "output_format": "Output Format", "output_quality": "Output Quality", "disable_safety_checker": "Disable Safety Checker", "prompt_strength": "Prompt Strength", "input_image": "Input Image (Optional)", "safety_tolerance": "Safety Tolerance", "safety_filter_level": "Safety Filter Level", "size": "Image Size", "guidance_scale": "Guidance Scale"}, "descriptions": {"width": "Width of the video in pixels (must be divisible by 16)", "height": "Height of the video in pixels (must be divisible by 16)", "video_length": "Number of frames to generate (must be 4k+1, ex: 49 or 129)", "infer_steps": "Number of denoising steps. Higher values improve quality but take longer.", "embedded_guidance_scale": "Controls how closely the model follows the prompt. Higher values = more adherence.", "fps": "Frames per second of the output video", "seed": "Random seed for reproducible generation. Leave empty for random.", "image": "Input image to start generating from. Ideal images are 16:9 or 9:16 and 1280x720 or 720x1280, depending on the aspect ratio you choose.", "aspect_ratio": "Video aspect ratio", "duration": "Video duration in seconds", "enhance_prompt": "Use AI to enhance your prompt for better results", "negative_prompt": "Description of what to discourage in the generated video", "resolution": "Video resolution", "camera_fixed": "Whether to fix camera position", "num_outputs": "Number of outputs to generate", "num_inference_steps": "Number of denoising steps. Recommended range is 28-50. Lower values are faster but lower quality.", "guidance": "Guidance for generated image. Lower values can give more realistic images. Good values: 2, 2.5, 3, 3.5", "go_fast": "Run faster predictions with additional optimizations", "megapixels": "Approximate number of megapixels for generated image", "output_format": "Format of the output images", "output_quality": "Quality when saving the output images, from 0 to 100. 100 is best quality. Not relevant for PNG outputs", "disable_safety_checker": "Disable safety checker for generated images", "prompt_strength": "Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image", "input_image": "Image to use as reference. Must be jpeg, png, gif, or webp.", "safety_tolerance": "Safety tolerance, 0 is most strict and 6 is most permissive. 2 is currently the maximum allowed when input images are used.", "safety_filter_level": "Content safety filtering level. \"block_low_and_above\" is strictest, \"block_medium_and_above\" blocks some prompts, \"block_only_high\" is most permissive but some prompts will still be blocked", "size": "Big images will have their longest dimension be 2048px. Small images will have their shortest dimension be 512px. Regular images will always be 1 megapixel. Ignored if aspect ratio is custom.", "guidance_scale": "Prompt adherence. Higher values = more literal interpretation of the prompt."}, "placeholders": {"seed": "Leave empty for random", "negative_prompt": "e.g., blurry, low quality, distorted...", "aspect_ratio": "Select an aspect ratio", "output_format": "Select output format"}}, "options": {"aspectRatios": {"1:1": "1:1 (Square)", "16:9": "16:9 (Landscape)", "9:16": "9:16 (Portrait)", "4:3": "4:3 (Standard)", "3:4": "3:4 (Portrait)", "21:9": "21:9 (Ultrawide)", "9:21": "9:21 (Tall Portrait)", "3:2": "3:2 (Photo)", "2:3": "2:3 (Photo Portrait)", "4:5": "4:5 (Portrait)", "5:4": "5:4 (Landscape)", "2:1": "2:1 (<PERSON>)", "1:2": "1:2 (<PERSON>)", "match_input_image": "Match Input Image", "custom": "Custom (specify width/height)"}, "videoLength": {"49": "49 frames (~2 seconds)", "129": "129 frames (~5 seconds)"}, "duration": {"5": "5 seconds", "6": "6 seconds", "7": "7 seconds", "8": "8 seconds", "10": "10 seconds"}, "resolution": {"480p": "480p (Faster)", "720p": "720p (Standard)", "1080p": "1080p (Higher Quality)"}, "imageCount": {"1": "1 image", "2": "2 images", "4": "4 images"}, "formats": {"png": "PNG", "jpg": "JPEG", "webp": "WebP (Recommended)"}, "megapixels": {"0.25": "0.25 MP (Faster)", "1": "1 MP (Standard)"}, "safetyFilterLevel": {"block_low_and_above": "Strict (Block Low and Above)", "block_medium_and_above": "Moderate (Block Medium and Above)", "block_only_high": "Permissive (Block Only High)"}, "imageSize": {"small": "Small (512px shortest)", "regular": "Regular (1 megapixel)", "big": "Big (2048px longest)"}, "boolean": {"enabled": "Enabled", "disabled": "Disabled"}}, "generateButton": "Generate", "generating": "Generating...", "generationFailed": "Generation failed. Please try again.", "uploadSection": {"title": "Upload an image to animate", "description": "Drag and drop or click to browse", "chooseImage": "Choose Image"}, "activeGenerations": {"title": "Active Generations", "videoGeneration": "Video Generation", "imageGeneration": "Image Generation", "generating": "Generating your {type}... This may take a few seconds.", "generationFailed": "Generation failed. Please try again.", "download": "Download", "downloadVideo": "Download Video", "downloadImage": "Download Image", "generatedImageAlt": "Generated image"}, "modelSpecific": {"seedreamSpecific": "Seedream-3 Specific", "imagenSpecific": "Imagen-4-Ultra Specific", "fluxSpecific": "FLUX Kontext Pro Specific", "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "safetyFilterLevel": "Safety Filter Level", "megapixels": "Megapixels", "safetyTolerance": "Safety Tolerance", "promptStrength": "Prompt Strength", "fastGeneration": "Fast Generation", "disableSafetyChecker": "Disable Safety Checker", "outputQuality": "Output Quality", "safetyOptions": {"blockMost": "Block Most", "blockSome": "Block Some", "blockFew": "Block Few"}, "toleranceOptions": {"low": "Low (1)", "medium": "Medium (2)", "high": "High (3)", "veryHigh": "Very High (4)", "maximum": "Maximum (5)"}}, "statusMessages": {"generating": "Generating your {{type}}... This may take a few seconds.", "generationFailed": "Generation failed. Error: {{error}}", "browserNotSupported": "Your browser does not support the video tag.", "notEnoughTokens": "Not enough tokens. Required: {{amount}} tokens", "tokensAmount": " {{amount}} tokens"}, "toastMessages": {"videoGenerationStarted": "Video Generation Started", "imageGenerationStarted": "Image Generation Started", "generationStartedDescription": "Your {{type}} is being generated. You will be notified when it's ready.", "success": "Success", "error": "Error", "videoGeneratedSuccess": "{{type}} generated successfully!", "generationFailedDescription": "{{type}} generation failed. Error: {{error}}", "failedToStart": "Failed to start {{type}} generation. Please try again."}, "types": {"video": "video", "image": "image"}, "settings": {"advancedSettings": "Advanced Settings", "advancedSettingsDescription": "Fine-tune generation parameters"}, "imageSpecific": {"fluxDevConfig": {"modelName": "FLUX Dev", "description": "A 12 billion parameter rectified flow transformer capable of generating images from text descriptions", "parameters": {"image": {"label": "Input Image (Optional)", "description": "Input image for image-to-image mode. The aspect ratio of your output will match this image"}, "prompt_strength": {"label": "Prompt Strength", "description": "Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image"}, "num_outputs": {"label": "Number of Images", "description": "Number of outputs to generate", "options": {"1": "1 image", "2": "2 images", "4": "4 images"}}, "num_inference_steps": {"label": "Inference Steps", "description": "Number of denoising steps. Recommended range is 28-50. Lower values are faster but lower quality."}, "guidance": {"label": "Guidance", "description": "Guidance for generated image. Lower values can give more realistic images. Good values: 2, 2.5, 3, 3.5"}, "go_fast": {"label": "Go Fast", "description": "Run faster predictions with additional optimizations"}, "megapixels": {"label": "Megapixels", "description": "Approximate number of megapixels for generated image", "options": {"0.25": "0.25 MP (Faster)", "1": "1 MP (Standard)"}}, "output_quality": {"label": "Output Quality", "description": "Quality when saving the output images, from 0 to 100. 100 is best quality. Not relevant for PNG outputs"}, "disable_safety_checker": {"label": "Disable Safety Checker", "description": "Disable safety checker for generated images"}}}, "fluxKontextProConfig": {"modelName": "FLUX Kontext Pro", "description": "A state-of-the-art text-based image editing model that delivers high-quality outputs with excellent prompt following and consistent results for transforming images through natural language", "parameters": {"input_image": {"label": "Input Image (Optional)", "description": "Image to use as reference. Must be jpeg, png, gif, or webp."}, "safety_tolerance": {"label": "Safety Tolerance", "description": "Safety tolerance, 0 is most strict and 6 is most permissive. 2 is currently the maximum allowed when input images are used."}}}, "imagen4UltraConfig": {"modelName": "Imagen 4 Ultra", "description": "Use this ultra version of Imagen 4 when quality matters more than speed and cost", "parameters": {"safety_filter_level": {"label": "Safety Filter Level", "description": "Content safety filtering level. \"block_low_and_above\" is strictest, \"block_medium_and_above\" blocks some prompts, \"block_only_high\" is most permissive but some prompts will still be blocked", "options": {"block_low_and_above": {"label": "Strict (Block Low and Above)", "description": "Most restrictive filtering"}, "block_medium_and_above": {"label": "Moderate (Block Medium and Above)", "description": "Balanced filtering"}, "block_only_high": {"label": "Permissive (Block Only High)", "description": "Least restrictive filtering"}}}}}, "seedream3Config": {"modelName": "Seedream 3", "description": "A text-to-image model with support for native high-resolution (2K) image generation", "parameters": {"size": {"label": "Image Size", "description": "Big images will have their longest dimension be 2048px. Small images will have their shortest dimension be 512px. Regular images will always be 1 megapixel. Ignored if aspect ratio is custom.", "options": {"small": "Small (512px shortest)", "regular": "Regular (1 megapixel)", "big": "Big (2048px longest)"}}, "width": {"label": "<PERSON><PERSON><PERSON>", "description": "Image width in pixels"}, "height": {"label": "Height", "description": "Image height in pixels"}, "guidance_scale": {"label": "Guidance Scale", "description": "Prompt adherence. Higher values = more literal interpretation of the prompt."}}}, "common": {"parameters": {"aspect_ratio": {"label": "Aspect Ratio", "description": "Aspect ratio of the generated image", "options": {"1:1": "1:1 (Square)", "16:9": "16:9 (Landscape)", "21:9": "21:9 (Ultrawide)", "3:2": "3:2 (Photo)", "2:3": "2:3 (Photo Portrait)", "4:5": "4:5 (Portrait)", "5:4": "5:4 (Landscape)", "3:4": "3:4 (Portrait)", "4:3": "4:3 (Standard)", "9:16": "9:16 (Mobile Portrait)", "9:21": "9:21 (Tall Portrait)", "match_input_image": "Match Input Image", "2:1": "2:1 (<PERSON>)", "1:2": "1:2 (<PERSON>)", "custom": "Custom (specify width/height)"}}, "output_format": {"label": "Output Format", "description": "Format of the output images", "options": {"webp": "WebP (Recommended)", "jpg": "JPEG", "png": "PNG"}}, "seed": {"title": "Seed", "description": "Random seed. Set for reproducible generation"}}}}}, "contentGallery": {"title": "Recent Creations", "sortBy": "Sort by:", "sortOptions": {"mostRecent": "Most Recent", "mostLiked": "Most Liked", "mostViewed": "Most Viewed"}, "filterBy": "Filter:", "filterOptions": {"all": "All", "videos": "Videos", "images": "Images"}, "views": "views"}, "generationHistory": {"title": "Generation History", "description": "View all your previous video and image generations", "showing": "Showing", "to": "to", "of": "of", "generations": "generations", "show": "Show", "perPage": "per page", "filters": {"all": "All", "images": "Images", "videos": "Videos"}, "noGenerations": "No generations found", "loadMore": "Load More", "model": "Model", "error": "Error", "pagination": {"previous": "Previous", "next": "Next"}, "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed"}, "type": {"video": "Video", "image": "Image"}, "copyPrompt": "Copy prompt", "copySuccess": "Prompt copied!", "copySuccessDescription": "The prompt has been copied to your clipboard.", "copyError": "Co<PERSON> failed", "copyErrorDescription": "Failed to copy the prompt. Please try again.", "noGenerationsYet": "No generations yet", "noGenerationsDescription": "Start creating videos and images to see your history here."}, "notFound": {"title": "404", "message": "Oops! Page not found", "returnHome": "Return to Home"}, "privacyPolicy": {"title": "Privacy Policy", "lastUpdated": "Last updated: January 2025", "content": "At Holst AI, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, and safeguard your data when you use our AI-powered creative platform.\n\nWhen you create an account, we collect basic information such as your email address, name, and authentication details through OAuth providers like Google. We do not store passwords directly. We also collect information about how you use our service, including the types of content you generate, usage patterns, and technical data such as IP addresses, browser type, and device information. Additionally, we store the prompts you submit and the AI-generated content (images and videos) you create, which is associated with your account and used to provide our services.\n\nWe use your information to provide, maintain, and improve our AI generation services, analyze usage patterns to enhance our platform's performance, communicate with you about your account, updates, and support, and ensure the security and integrity of our platform.\n\nWe implement industry-standard security measures to protect your personal information and generated content. All data is encrypted in transit and at rest. We regularly review and update our security practices to ensure your information remains protected.\n\nWe do not sell, trade, or rent your personal information to third parties. We may share your information only when required by law or to comply with legal processes, with your explicit consent, or with trusted service providers who assist in operating our platform under strict confidentiality agreements.\n\nYou have the right to access and review your personal information, request correction of inaccurate or incomplete data, request deletion of your account and associated data, and request a copy of your data in a portable format.\n\nIf you have any questions about this Privacy Policy or how we handle your data, please contact <NAME_EMAIL>. We are committed to addressing your concerns and ensuring your privacy rights are respected."}, "termsOfUse": {"title": "Terms of Use", "lastUpdated": "Last updated: January 2025", "content": "Welcome to Holst AI. These Terms of Use (\"Terms\") govern your use of our AI-powered creative platform and services. By accessing or using our service, you agree to be bound by these Terms.\n\n1. ACCEPTANCE OF TERMS\nBy creating an account or using our service, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy. If you do not agree to these Terms, please do not use our service.\n\n2. DESCRIPTION OF SERVICE\nHolst AI provides an AI-powered platform for generating images and videos from text descriptions. Our service uses advanced artificial intelligence models to create visual content based on your prompts and specifications.\n\n3. USER ACCOUNTS\nTo use our service, you must create an account and provide accurate, complete information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You must be at least 18 years old to use our service.\n\n4. ACCEPTABLE USE\nYou agree to use our service only for lawful purposes and in accordance with these Terms. You may not:\n- Generate content that is illegal, harmful, threatening, abusive, or violates any laws\n- Create content that infringes on intellectual property rights of others\n- Use our service to generate misleading, false, or deceptive content\n- Attempt to reverse engineer, hack, or compromise our systems\n- Use our service for commercial purposes without proper licensing\n\n5. INTELLECTUAL PROPERTY\nYou retain ownership of the content you generate using our service. However, by using our service, you grant us a limited license to store, process, and display your content as necessary to provide our services. Our AI models, software, and platform remain our intellectual property.\n\n6. PAYMENT AND SUBSCRIPTIONS\nCertain features of our service require payment. All fees are non-refundable unless otherwise specified. We reserve the right to change our pricing at any time with reasonable notice.\n\n7. LIMITATION OF LIABILITY\nOur service is provided \"as is\" without warranties of any kind. We shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our service.\n\n8. TERMINATION\nWe may terminate or suspend your account at any time for violation of these Terms. You may terminate your account at any time by contacting us.\n\n9. CHANGES TO TERMS\nWe reserve the right to modify these Terms at any time. We will notify you of significant changes via email or through our service.\n\n10. CONTACT INFORMATION\nIf you have questions about these Terms, please contact <NAME_EMAIL>."}, "seo": {"main": {"title": "Holst AI - Create Stunning Images and Videos with AI", "description": "Transform your ideas into incredible images and videos using our most intelligent AI models. Professional quality results in seconds without VPN or limitations.", "keywords": "AI generation, image creation, video generation, artificial intelligence, neural networks, creativity, design, content"}, "smm": {"title": "Create Content for SMM with AI - Holst AI", "description": "Simplify and speed up the process of creating content for posts on social networks. Create eye-catching visuals, inspiring and attention-grabbing.", "keywords": "SMM content, social media, social visuals, content marketing, AI for SMM, post creation"}, "marketplace": {"title": "Create Marketplace Covers with AI - Holst AI", "description": "Upload a photo or simply describe a product - AI will prepare ready-made images and videos that meet the standards of OZON, Wildberries, Amazon, and other platforms.", "keywords": "marketplace covers, OZON, Wildberries, Amazon, product photos, 3D renders, promotional banners"}, "inspiration": {"title": "Find Inspiration with AI - Holst AI", "description": "No more hours spent on Pinterest or endless scrolling through social networks. Describe a mood, theme or idea - and AI will select visuals, color palettes and compositions.", "keywords": "inspiration, creative ideas, color palettes, visual references, design ideas, creative process"}, "livingPhoto": {"title": "Bring Photos to Life with AI - Holst AI", "description": "Transform ordinary images into dynamic and lively visuals. AI adds movement, emotions, and atmosphere, creating animations that grab attention.", "keywords": "photo animation, image animation, living photos, dynamic visuals, social media animation"}, "influencer": {"title": "Create Virtual Influencer with AI - Holst AI", "description": "Come up with the perfect digital hero for social media. You set the appearance, style and character - AI creates a unique influencer, ready to manage accounts.", "keywords": "virtual influencer, digital character, Instagram, TikTok, YouTube, character creation"}}}